"use client";
import React, { useEffect, useState } from "react";
import { useFormik, FormikErrors } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchNemtDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  FetchDriverDetailsResponse,
} from "@/services/driverFormService";
import { getStates, State } from "@/services/locationService";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtWorkHistory.module.scss';

interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}

interface WorkPeriod {
  id: string;
  typeOfEmployment: string;
  isUnemployment: boolean;
  employerName: string;
  address: Address;
  employerManagerName: string;
  phone: string;
  employerWebsite: string;
  position: string;
  subjectToFmcsa: string;
  operatedCmv: string;
  contactPermission: string;
  startDate: Date | null;
  endDate: Date | null;
  isCurrent: boolean;
  reasonForLeaving: string;
  explanation: string;
}

interface WorkHistoryFormValues {
  workHistory: WorkPeriod[];
}

const NemtWorkHistory: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [states, setStates] = useState<State[]>([]);
  const [employmentTypeOptions, setEmploymentTypeOptions] = useState<FormValue[]>([]);
  const [reasonOptions, setReasonOptions] = useState<FormValue[]>([]);

  const createEmptyWorkPeriod = (): WorkPeriod => ({
    id: Date.now().toString(),
    typeOfEmployment: "",
    isUnemployment: false,
    employerName: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
    },
    employerManagerName: "",
    phone: "",
    employerWebsite: "",
    position: "",
    subjectToFmcsa: "no",
    operatedCmv: "no",
    contactPermission: "yes",
    startDate: null,
    endDate: null,
    isCurrent: false,
    reasonForLeaving: "",
    explanation: "",
  });

  const validationSchema = Yup.object({
    workHistory: Yup.array()
      .of(
        Yup.object({
          typeOfEmployment: Yup.string().required("Employment type is required"),
          employerName: Yup.string().when("isUnemployment", {
            is: false,
            then: (schema) => schema.required("Employer name is required"),
            otherwise: (schema) => schema,
          }),
          address: Yup.object({
            street: Yup.string().when("isUnemployment", {
              is: false,
              then: (schema) => schema.required("Street address is required"),
              otherwise: (schema) => schema,
            }),
            city: Yup.string().when("isUnemployment", {
              is: false,
              then: (schema) => schema.required("City is required"),
              otherwise: (schema) => schema,
            }),
            state: Yup.string().when("isUnemployment", {
              is: false,
              then: (schema) => schema.required("State is required"),
              otherwise: (schema) => schema,
            }),
            zipCode: Yup.string().when("isUnemployment", {
              is: false,
              then: (schema) => schema.required("Zip code is required"),
              otherwise: (schema) => schema,
            }),
          }),
          phone: Yup.string().when("isUnemployment", {
            is: false,
            then: (schema) => schema.required("Phone number is required"),
            otherwise: (schema) => schema,
          }),
          position: Yup.string().when("isUnemployment", {
            is: false,
            then: (schema) => schema.required("Position is required"),
            otherwise: (schema) => schema,
          }),
          contactPermission: Yup.string().when("isUnemployment", {
            is: false,
            then: (schema) => schema.required("Contact permission is required"),
            otherwise: (schema) => schema,
          }),
          startDate: Yup.date().required("Start date is required"),
          endDate: Yup.date().when("isCurrent", {
            is: false,
            then: (schema) => schema.required("End date is required"),
            otherwise: (schema) => schema.nullable(),
          }),
          reasonForLeaving: Yup.string().required("Reason for leaving is required"),
          explanation: Yup.string().when("reasonForLeaving", {
            is: (val: string) => val && (val.includes("other") || val.includes("terminated") || val.includes("unemployment")),
            then: (schema) => schema.required("Explanation is required").max(250, "Maximum 250 characters"),
            otherwise: (schema) => schema.max(250, "Maximum 250 characters"),
          }),
        })
      )
      .min(1, "At least one work period is required")
      .required("Work history is required"),
  });

  const formik = useFormik<WorkHistoryFormValues>({
    initialValues: {
      workHistory: [createEmptyWorkPeriod()],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load states and driver details in parallel
        const [statesData, formFieldsResponse, driverDetails] = await Promise.all([
          getStates(),
          fetchNemtDriverFormFields([
            "type-of-period-driver-school-bus-driver",
            "reason-for-leaving-ending-period-driver-school-bus-driver",
          ]),
          fetchDriverDetails()
        ]);

        setStates(statesData);

        // Load form field options
        if (formFieldsResponse?.status && formFieldsResponse?.data) {
          const fields = formFieldsResponse.data;

          const employmentTypeKey = "type-of-period-driver-school-bus-driver";
          if (fields[employmentTypeKey]?.formValues) {
            setEmploymentTypeOptions(fields[employmentTypeKey].formValues);
          }

          const reasonKey = "reason-for-leaving-ending-period-driver-school-bus-driver";
          if (fields[reasonKey]?.formValues) {
            setReasonOptions(fields[reasonKey].formValues);
          }
        }

        // Pre-populate with existing data if available
        const history = driverDetails?.data?.driver?.driverEmploymentHistory;
        if (history && history.length > 0) {
          const mappedHistory = history.map((emp: any) => ({
            id: `work-${emp.driverEmploymentHistoryId || Date.now()}-${Math.random()}`,
            typeOfEmployment: emp.typeOfEmployment?.toString() || "",
            isUnemployment: emp.isUnemployment || false,
            employerName: emp.employerName || "",
            address: {
              street: emp.employerStreet || "",
              city: emp.employerCity || "",
              state: emp.employerState || "",
              zipCode: emp.employerZip || "",
            },
            employerManagerName: emp.employerManagerName || "",
            phone: emp.employerPhone || "",
            employerWebsite: emp.employerWebsite || "",
            position: emp.positionHeld || "",
            subjectToFmcsa: emp.subjectToFmcsa ? "yes" : "no",
            operatedCmv: emp.operatedCmv ? "yes" : "no",
            contactPermission: emp.contactPermission ? "yes" : "no",
            startDate: emp.startDate ? new Date(emp.startDate) : null,
            endDate: emp.endDate ? new Date(emp.endDate) : null,
            isCurrent: emp.isCurrent || false,
            reasonForLeaving: emp.reasonForLeaving || "",
            explanation: emp.explanation || "",
          }));

          formik.setFieldValue("workHistory", mappedHistory);
        }
      } catch (error) {
        console.error("Error loading form fields:", error);
        toast.error("Failed to load form options. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (values: WorkHistoryFormValues, shouldContinue: boolean) => {
    try {
      setIsLoading(true);

      const payload = {
        currentStage: 3,
        currentStep: 2,
        driver: {
          driverEmploymentHistory: values.workHistory.map((period, index) => ({
            typeOfEmployment: period.typeOfEmployment ? parseInt(period.typeOfEmployment) : 123, // Default value as per API spec
            isUnemployment: period.isUnemployment,
            employerName: period.isUnemployment ? undefined : period.employerName,
            employerStreet: period.isUnemployment ? undefined : period.address.street,
            employerCity: period.isUnemployment ? undefined : period.address.city,
            employerState: period.isUnemployment ? undefined : period.address.state,
            employerManagerName: period.isUnemployment ? undefined : period.employerManagerName,
            employerZip: period.isUnemployment ? undefined : period.address.zipCode,
            employerPhone: period.isUnemployment ? undefined : period.phone,
            employerWebsite: period.isUnemployment ? undefined : period.employerWebsite,
            positionHeld: period.isUnemployment ? undefined : period.position,
            subjectToFmcsa: period.isUnemployment ? undefined : period.subjectToFmcsa === "yes",
            operatedCmv: period.isUnemployment ? undefined : period.operatedCmv === "yes",
            contactPermission: period.isUnemployment ? undefined : period.contactPermission === "yes",
            startDate: period.startDate ? period.startDate.toISOString() : null,
            endDate: period.isCurrent ? undefined : (period.endDate ? period.endDate.toISOString() : null),
            isCurrent: period.isCurrent,
            reasonForLeaving: period.reasonForLeaving,
            explanation: period.explanation || "",
            rank: index + 1,
          })),
        },
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Work history saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save work history. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting work history:", error);
      toast.error("Failed to save work history. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const addWorkPeriod = () => {
    const newPeriod = createEmptyWorkPeriod();
    formik.setFieldValue("workHistory", [...formik.values.workHistory, newPeriod]);
  };

  const removeWorkPeriod = (index: number) => {
    if (formik.values.workHistory.length > 1) {
      const updatedHistory = formik.values.workHistory.filter((_, i) => i !== index);
      formik.setFieldValue("workHistory", updatedHistory);
    }
  };

  const isWorkPeriodErrorArray = (errors: any): errors is FormikErrors<WorkPeriod>[] => {
    return Array.isArray(errors);
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.workHistory}>
      <h3>Step 2: Work History (Last 3 Years Required)</h3>
      <div className={css.note}>
        <h6>Important:</h6>
        <p>Provide a complete history for the <strong>past 3 years</strong>. Include all NEMT driving roles, other employment, and any unemployment periods. Verification is important.</p>
      </div>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>

      <form onSubmit={formik.handleSubmit}>
        <div className={css.workHistorySection}>
          <label>Work / Unemployment Periods (Start with most recent)</label>
          <button
            type="button"
            onClick={addWorkPeriod}
            className={css.addPeriodBtn}
          >
            + Add Work/Unemployment Period
          </button>

          {formik.values.workHistory.map((period, index) => (
            <div key={period.id} className={css.periodBlock}>
              <h4>Period {index + 1}</h4>

              {/* Employment Type */}
              <div className={css.formRow}>
                <label>Type of Employment: *</label>
                <Dropdown
                  options={employmentTypeOptions.map(option => ({ value: option.formValueId.toString(), label: option.label.en }))}
                  value={period.typeOfEmployment}
                  placeholder="Select Employment Type"
                  onChange={(value) => {
                    formik.setFieldValue(`workHistory[${index}].typeOfEmployment`, value);
                    // Auto-set unemployment status based on selection
                    const isUnemploymentType = value === "unemployment-period" || employmentTypeOptions.find(opt => opt.formValueId.toString() === value)?.label.en.toLowerCase().includes("unemployment");
                    formik.setFieldValue(`workHistory[${index}].isUnemployment`, isUnemploymentType);
                  }}
                  error={formik.touched.workHistory?.[index]?.typeOfEmployment &&
                    isWorkPeriodErrorArray(formik.errors.workHistory) &&
                    formik.errors.workHistory[index]?.typeOfEmployment ?
                    formik.errors.workHistory[index]?.typeOfEmployment : undefined}
                  name={`workHistory[${index}].typeOfEmployment`}
                />
              </div>

              {/* Conditional fields based on employment type */}
              {!period.isUnemployment && (
                <>
                      {/* Company Employee Fields */}
                      <div className={css.formRow}>
                        <label>Employer Name: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].employerName`}
                          value={period.employerName}
                          onChange={formik.handleChange}
                          placeholder="Enter Employer Name"
                          className={css.input}
                        />
                        {formik.touched.workHistory?.[index]?.employerName &&
                          isWorkPeriodErrorArray(formik.errors.workHistory) &&
                          formik.errors.workHistory[index]?.employerName && (
                            <p className={css.error}>
                              {formik.errors.workHistory[index]?.employerName}
                            </p>
                          )}
                      </div>

                      {/* Manager Name */}
                      <div className={css.formRow}>
                        <label>Manager/Supervisor Name:</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].employerManagerName`}
                          value={period.employerManagerName}
                          onChange={formik.handleChange}
                          placeholder="Enter Manager Name"
                          className={css.input}
                        />
                      </div>

                      {/* Website */}
                      <div className={css.formRow}>
                        <label>Company Website (Optional):</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].employerWebsite`}
                          value={period.employerWebsite}
                          onChange={formik.handleChange}
                          placeholder="www.company.com"
                          className={css.input}
                        />
                      </div>

                      {/* Company Address */}
                      <div className={css.formRow}>
                        <label>Company Full Address: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].address.street`}
                          value={period.address.street}
                          onChange={formik.handleChange}
                          placeholder="Street Address"
                          className={css.input}
                        />
                        <input
                          type="text"
                          name={`workHistory[${index}].address.city`}
                          value={period.address.city}
                          onChange={formik.handleChange}
                          placeholder="City"
                          className={css.input}
                        />
                        <Dropdown
                          options={states.map(state => ({ value: state.slug, label: state.name.en }))}
                          value={period.address.state}
                          placeholder="Select State"
                          onChange={(value) => formik.setFieldValue(`workHistory[${index}].address.state`, value)}
                          name={`workHistory[${index}].address.state`}
                        />
                        <input
                          type="text"
                          name={`workHistory[${index}].address.zipCode`}
                          value={period.address.zipCode}
                          onChange={formik.handleChange}
                          placeholder="Zip Code"
                          className={css.input}
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Company Phone Number (Supervisor/HR): *</label>
                        <input
                          type="tel"
                          name={`workHistory[${index}].phone`}
                          value={period.phone}
                          onChange={formik.handleChange}
                          placeholder="(*************"
                          className={css.input}
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Position Held: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].position`}
                          value={period.position}
                          onChange={formik.handleChange}
                          placeholder="e.g., NEMT Driver, Patient Transporter, Scheduler"
                          className={css.input}
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>May we contact this Company for verification? *</label>
                        <RadioGroup
                          name={`workHistory[${index}].contactPermission`}
                          options={["yes", "no"]}
                          selectedValue={period.contactPermission}
                          onChange={(value) =>
                            formik.setFieldValue(`workHistory[${index}].contactPermission`, value)
                          }
                        />
                      </div>

                  {/* Common fields for all employment types */}
                  <div className={css.formRow}>
                    <label>Were you subject to FMCSA Drug & Alcohol Testing in this role?</label>
                    <RadioGroup
                      name={`workHistory[${index}].subjectToFmcsa`}
                      options={["yes", "no"]}
                      selectedValue={period.subjectToFmcsa}
                      onChange={(value) =>
                        formik.setFieldValue(`workHistory[${index}].subjectToFmcsa`, value)
                      }
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>Did you operate a Commercial Motor Vehicle (CMV) in this role?</label>
                    <RadioGroup
                      name={`workHistory[${index}].operatedCmv`}
                      options={["yes", "no"]}
                      selectedValue={period.operatedCmv}
                      onChange={(value) =>
                        formik.setFieldValue(`workHistory[${index}].operatedCmv`, value)
                      }
                    />
                  </div>
                </>
              )}

              {/* Dates - shown for all period types */}
              <div className={css.formRow}>
                <div className={css.dateFields}>
                  <div className={css.dateField}>
                    <label>Start Date - From *</label>
                    <DateInput
                      selected={period.startDate}
                      onChange={(date) =>
                        formik.setFieldValue(`workHistory[${index}].startDate`, date)
                      }
                    />
                  </div>
                  <div className={css.dateField}>
                    <label>End Date - To *</label>
                    <DateInput
                      selected={period.endDate}
                      onChange={(date) =>
                        formik.setFieldValue(`workHistory[${index}].endDate`, date)
                      }
                      disabled={period.isCurrent}
                    />
                  </div>
                  <div className={css.checkboxField}>
                    <input
                      type="checkbox"
                      name={`workHistory[${index}].isCurrent`}
                      checked={period.isCurrent}
                      onChange={(e) => {
                        formik.setFieldValue(`workHistory[${index}].isCurrent`, e.target.checked);
                        if (e.target.checked) {
                          formik.setFieldValue(`workHistory[${index}].endDate`, null);
                        }
                      }}
                    />
                    <label>I am currently working in this role</label>
                  </div>
                </div>
              </div>

              {/* Reason for leaving */}
              <div className={css.formRow}>
                <label>Reason for Leaving / Ending Period: *</label>
                <Dropdown
                  options={reasonOptions.map(option => ({ value: option.formValueSlug, label: option.label.en }))}
                  value={period.reasonForLeaving}
                  placeholder="Select Reason"
                  onChange={(value) => formik.setFieldValue(`workHistory[${index}].reasonForLeaving`, value)}
                  name={`workHistory[${index}].reasonForLeaving`}
                />
              </div>

              {/* Explanation - conditional */}
              {(period.reasonForLeaving.includes("other") ||
                period.reasonForLeaving.includes("terminated") ||
                period.reasonForLeaving.includes("unemployment")) && (
                <div className={css.formRow}>
                  <label>Brief Explanation: *</label>
                  <textarea
                    name={`workHistory[${index}].explanation`}
                    value={period.explanation}
                    onChange={formik.handleChange}
                    placeholder="Briefly explain reason if needed... Max 250 characters"
                    maxLength={250}
                    className={css.textarea}
                  />
                </div>
              )}

              {formik.values.workHistory.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeWorkPeriod(index)}
                  className={css.removeBtn}
                >
                  Remove This Period
                </button>
              )}
            </div>
          ))}
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 1: Experience)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              className={css.saveExitBtn}
              disabled={isLoading}
            >
              Save & Exit (Complete Later)
            </button>
            <button
              type="submit"
              className={css.continueBtn}
              disabled={isLoading}
            >
              Save & Continue (To Step 3: Medical) →
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtWorkHistory;