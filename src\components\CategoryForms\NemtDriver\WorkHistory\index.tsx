"use client";
import React, { useEffect, useState } from "react";
import { useFormik, FormikErrors } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchNemtDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  FetchDriverDetailsResponse,
} from "@/services/driverFormService";
import { getStates, State } from "@/services/locationService";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtWorkHistory.module.scss';

interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}

interface WorkPeriod {
  id: string;
  periodType: string;
  companyName: string;
  address: Address;
  phone: string;
  position: string;
  subjectToTesting: string;
  operatedCdl: string;
  mayContact: string;
  brokersPlatforms: string;
  verificationContact: string;
  verificationPhone: string;
  startDate: Date | null;
  endDate: Date | null;
  currentlyWorking: boolean;
  reasonForLeaving: string;
  explanation: string;
}

interface WorkHistoryFormValues {
  workHistory: WorkPeriod[];
}

const NemtWorkHistory: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [states, setStates] = useState<State[]>([]);
  const [periodTypeOptions, setPeriodTypeOptions] = useState<FormValue[]>([]);
  const [reasonOptions, setReasonOptions] = useState<FormValue[]>([]);

  const createEmptyWorkPeriod = (): WorkPeriod => ({
    id: Date.now().toString(),
    periodType: "",
    companyName: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
    },
    phone: "",
    position: "",
    subjectToTesting: "unsure",
    operatedCdl: "no",
    mayContact: "yes",
    brokersPlatforms: "",
    verificationContact: "",
    verificationPhone: "",
    startDate: null,
    endDate: null,
    currentlyWorking: false,
    reasonForLeaving: "",
    explanation: "",
  });

  const validationSchema = Yup.object({
    workHistory: Yup.array()
      .of(
        Yup.object({
          periodType: Yup.string().required("Period type is required"),
          companyName: Yup.string().when("periodType", {
            is: (val: string) => val && val !== "unemployment-period",
            then: (schema) => schema.required("Company name is required"),
            otherwise: (schema) => schema,
          }),
          address: Yup.object({
            street: Yup.string().when("periodType", {
              is: (val: string) => val && val !== "unemployment-period" && val !== "independent-contractor",
              then: (schema) => schema.required("Street address is required"),
              otherwise: (schema) => schema,
            }),
            city: Yup.string().when("periodType", {
              is: (val: string) => val && val !== "unemployment-period" && val !== "independent-contractor",
              then: (schema) => schema.required("City is required"),
              otherwise: (schema) => schema,
            }),
            state: Yup.string().when("periodType", {
              is: (val: string) => val && val !== "unemployment-period" && val !== "independent-contractor",
              then: (schema) => schema.required("State is required"),
              otherwise: (schema) => schema,
            }),
            zipCode: Yup.string().when("periodType", {
              is: (val: string) => val && val !== "unemployment-period" && val !== "independent-contractor",
              then: (schema) => schema.required("Zip code is required"),
              otherwise: (schema) => schema,
            }),
          }),
          phone: Yup.string().when("periodType", {
            is: (val: string) => val && val !== "unemployment-period" && val !== "independent-contractor",
            then: (schema) => schema.required("Phone number is required"),
            otherwise: (schema) => schema,
          }),
          position: Yup.string().required("Position is required"),
          mayContact: Yup.string().when("periodType", {
            is: (val: string) => val && val !== "unemployment-period",
            then: (schema) => schema.required("Contact permission is required"),
            otherwise: (schema) => schema,
          }),
          startDate: Yup.date().required("Start date is required"),
          endDate: Yup.date().when("currentlyWorking", {
            is: false,
            then: (schema) => schema.required("End date is required"),
            otherwise: (schema) => schema.nullable(),
          }),
          reasonForLeaving: Yup.string().required("Reason for leaving is required"),
          explanation: Yup.string().when("reasonForLeaving", {
            is: (val: string) => val && (val.includes("other") || val.includes("terminated") || val.includes("unemployment")),
            then: (schema) => schema.required("Explanation is required").max(250, "Maximum 250 characters"),
            otherwise: (schema) => schema.max(250, "Maximum 250 characters"),
          }),
        })
      )
      .min(1, "At least one work period is required")
      .required("Work history is required"),
  });

  const formik = useFormik<WorkHistoryFormValues>({
    initialValues: {
      workHistory: [createEmptyWorkPeriod()],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load states
        const statesData = await getStates();
        setStates(statesData);

        // Load form field options
        const formFieldsResponse = await fetchNemtDriverFormFields([
          "type-of-period-driver-school-bus-driver",
          "reason-for-leaving-ending-period-driver-school-bus-driver",
        ]);

        if (formFieldsResponse?.status && formFieldsResponse?.data) {
          const fields = formFieldsResponse.data;

          const periodTypeKey = "type-of-period-driver-school-bus-driver";
          if (fields[periodTypeKey]?.formValues) {
            setPeriodTypeOptions(fields[periodTypeKey].formValues);
          }

          const reasonKey = "reason-for-leaving-ending-period-driver-school-bus-driver";
          if (fields[reasonKey]?.formValues) {
            setReasonOptions(fields[reasonKey].formValues);
          }
        }
      } catch (error) {
        console.error("Error loading form fields:", error);
        toast.error("Failed to load form options. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (values: WorkHistoryFormValues, shouldContinue: boolean) => {
    try {
      setIsLoading(true);

      const payload = {
        nemtWorkHistory: values.workHistory.map(period => ({
          id: period.id,
          periodType: period.periodType,
          companyName: period.companyName,
          address: period.address,
          phone: period.phone,
          position: period.position,
          subjectToTesting: period.subjectToTesting,
          operatedCdl: period.operatedCdl,
          mayContact: period.mayContact,
          brokersPlatforms: period.brokersPlatforms,
          verificationContact: period.verificationContact,
          verificationPhone: period.verificationPhone,
          startDate: period.startDate?.toISOString(),
          endDate: period.endDate?.toISOString(),
          currentlyWorking: period.currentlyWorking,
          reasonForLeaving: period.reasonForLeaving,
          explanation: period.explanation,
        })),
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Work history saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save work history. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting work history:", error);
      toast.error("Failed to save work history. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const addWorkPeriod = () => {
    const newPeriod = createEmptyWorkPeriod();
    formik.setFieldValue("workHistory", [...formik.values.workHistory, newPeriod]);
  };

  const removeWorkPeriod = (index: number) => {
    if (formik.values.workHistory.length > 1) {
      const updatedHistory = formik.values.workHistory.filter((_, i) => i !== index);
      formik.setFieldValue("workHistory", updatedHistory);
    }
  };

  const isWorkPeriodErrorArray = (errors: any): errors is FormikErrors<WorkPeriod>[] => {
    return Array.isArray(errors);
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.workHistory}>
      <h3>Step 2: Work History (Last 3 Years Required)</h3>
      <div className={css.note}>
        <h6>Important:</h6>
        <p>Provide a complete history for the <strong>past 3 years</strong>. Include all NEMT driving roles, other employment, and any unemployment periods. Verification is important.</p>
      </div>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>

      <form onSubmit={formik.handleSubmit}>
        <div className={css.workHistorySection}>
          <label>Work / Unemployment Periods (Start with most recent)</label>
          <button
            type="button"
            onClick={addWorkPeriod}
            className={css.addPeriodBtn}
          >
            + Add Work/Unemployment Period
          </button>

          {formik.values.workHistory.map((period, index) => (
            <div key={period.id} className={css.periodBlock}>
              <h4>Period {index + 1}</h4>

              {/* Period Type */}
              <div className={css.formRow}>
                <label>Type of Period: *</label>
                <Dropdown
                  options={periodTypeOptions.map(option => ({ value: option.formValueSlug, label: option.label.en }))}
                  value={period.periodType}
                  placeholder="Select Type"
                  onChange={(value) => formik.setFieldValue(`workHistory[${index}].periodType`, value)}
                  error={formik.touched.workHistory?.[index]?.periodType &&
                    isWorkPeriodErrorArray(formik.errors.workHistory) &&
                    formik.errors.workHistory[index]?.periodType ?
                    formik.errors.workHistory[index]?.periodType : undefined}
                  name={`workHistory[${index}].periodType`}
                />
              </div>

              {/* Conditional fields based on period type */}
              {period.periodType && period.periodType !== "unemployment-period" && (
                <>
                  {period.periodType === "independent-contractor" ? (
                    <>
                      {/* Independent Contractor Fields */}
                      <div className={css.formRow}>
                        <label>Primary Broker(s) / Platform(s) Worked With (Optional):</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].brokersPlatforms`}
                          value={period.brokersPlatforms}
                          onChange={formik.handleChange}
                          placeholder="e.g., Modivcare, MTM, Uber Health, Self-Employed"
                          className={css.input}
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Position Held: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].position`}
                          value={period.position || "Independent Contractor / NEMT Driver"}
                          onChange={formik.handleChange}
                          className={css.input}
                          readOnly
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Verification Contact (Optional):</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].verificationContact`}
                          value={period.verificationContact}
                          onChange={formik.handleChange}
                          placeholder="Contact Name"
                          className={css.input}
                        />
                        <input
                          type="tel"
                          name={`workHistory[${index}].verificationPhone`}
                          value={period.verificationPhone}
                          onChange={formik.handleChange}
                          placeholder="(*************"
                          className={css.input}
                        />
                      </div>
                    </>
                  ) : (
                    <>
                      {/* Company Employee Fields */}
                      <div className={css.formRow}>
                        <label>Company / Agency Name: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].companyName`}
                          value={period.companyName}
                          onChange={formik.handleChange}
                          placeholder="Enter Company Name"
                          className={css.input}
                        />
                        {formik.touched.workHistory?.[index]?.companyName &&
                          isWorkPeriodErrorArray(formik.errors.workHistory) &&
                          formik.errors.workHistory[index]?.companyName && (
                            <p className={css.error}>
                              {formik.errors.workHistory[index]?.companyName}
                            </p>
                          )}
                      </div>

                      {/* Company Address */}
                      <div className={css.formRow}>
                        <label>Company Full Address: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].address.street`}
                          value={period.address.street}
                          onChange={formik.handleChange}
                          placeholder="Street Address"
                          className={css.input}
                        />
                        <input
                          type="text"
                          name={`workHistory[${index}].address.city`}
                          value={period.address.city}
                          onChange={formik.handleChange}
                          placeholder="City"
                          className={css.input}
                        />
                        <Dropdown
                          options={states.map(state => ({ value: state.slug, label: state.name.en }))}
                          value={period.address.state}
                          placeholder="Select State"
                          onChange={(value) => formik.setFieldValue(`workHistory[${index}].address.state`, value)}
                          name={`workHistory[${index}].address.state`}
                        />
                        <input
                          type="text"
                          name={`workHistory[${index}].address.zipCode`}
                          value={period.address.zipCode}
                          onChange={formik.handleChange}
                          placeholder="Zip Code"
                          className={css.input}
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Company Phone Number (Supervisor/HR): *</label>
                        <input
                          type="tel"
                          name={`workHistory[${index}].phone`}
                          value={period.phone}
                          onChange={formik.handleChange}
                          placeholder="(*************"
                          className={css.input}
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>Position Held: *</label>
                        <input
                          type="text"
                          name={`workHistory[${index}].position`}
                          value={period.position}
                          onChange={formik.handleChange}
                          placeholder="e.g., NEMT Driver, Patient Transporter, Scheduler"
                          className={css.input}
                        />
                      </div>

                      <div className={css.formRow}>
                        <label>May we contact this Company for verification? *</label>
                        <RadioGroup
                          name={`workHistory[${index}].mayContact`}
                          options={["yes", "no"]}
                          selectedValue={period.mayContact}
                          onChange={(value) =>
                            formik.setFieldValue(`workHistory[${index}].mayContact`, value)
                          }
                        />
                      </div>
                    </>
                  )}

                  {/* Common fields for all employment types */}
                  <div className={css.formRow}>
                    <label>Were you subject to Drug & Alcohol Testing in this role?</label>
                    <RadioGroup
                      name={`workHistory[${index}].subjectToTesting`}
                      options={["yes", "no", "unsure"]}
                      selectedValue={period.subjectToTesting}
                      onChange={(value) =>
                        formik.setFieldValue(`workHistory[${index}].subjectToTesting`, value)
                      }
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>Did you operate a vehicle requiring a CDL in this role?</label>
                    <RadioGroup
                      name={`workHistory[${index}].operatedCdl`}
                      options={["yes", "no"]}
                      selectedValue={period.operatedCdl}
                      onChange={(value) =>
                        formik.setFieldValue(`workHistory[${index}].operatedCdl`, value)
                      }
                    />
                  </div>
                </>
              )}

              {/* Dates - shown for all period types */}
              <div className={css.formRow}>
                <div className={css.dateFields}>
                  <div className={css.dateField}>
                    <label>Start Date - From *</label>
                    <DateInput
                      selected={period.startDate}
                      onChange={(date) =>
                        formik.setFieldValue(`workHistory[${index}].startDate`, date)
                      }
                    />
                  </div>
                  <div className={css.dateField}>
                    <label>End Date - To *</label>
                    <DateInput
                      selected={period.endDate}
                      onChange={(date) =>
                        formik.setFieldValue(`workHistory[${index}].endDate`, date)
                      }
                      disabled={period.currentlyWorking}
                    />
                  </div>
                  <div className={css.checkboxField}>
                    <input
                      type="checkbox"
                      name={`workHistory[${index}].currentlyWorking`}
                      checked={period.currentlyWorking}
                      onChange={(e) => {
                        formik.setFieldValue(`workHistory[${index}].currentlyWorking`, e.target.checked);
                        if (e.target.checked) {
                          formik.setFieldValue(`workHistory[${index}].endDate`, null);
                        }
                      }}
                    />
                    <label>I am currently working in this role</label>
                  </div>
                </div>
              </div>

              {/* Reason for leaving */}
              <div className={css.formRow}>
                <label>Reason for Leaving / Ending Period: *</label>
                <Dropdown
                  options={reasonOptions.map(option => ({ value: option.formValueSlug, label: option.label.en }))}
                  value={period.reasonForLeaving}
                  placeholder="Select Reason"
                  onChange={(value) => formik.setFieldValue(`workHistory[${index}].reasonForLeaving`, value)}
                  name={`workHistory[${index}].reasonForLeaving`}
                />
              </div>

              {/* Explanation - conditional */}
              {(period.reasonForLeaving.includes("other") ||
                period.reasonForLeaving.includes("terminated") ||
                period.reasonForLeaving.includes("unemployment")) && (
                <div className={css.formRow}>
                  <label>Brief Explanation: *</label>
                  <textarea
                    name={`workHistory[${index}].explanation`}
                    value={period.explanation}
                    onChange={formik.handleChange}
                    placeholder="Briefly explain reason if needed... Max 250 characters"
                    maxLength={250}
                    className={css.textarea}
                  />
                </div>
              )}

              {formik.values.workHistory.length > 1 && (
                <button
                  type="button"
                  onClick={() => removeWorkPeriod(index)}
                  className={css.removeBtn}
                >
                  Remove This Period
                </button>
              )}
            </div>
          ))}
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 1: Experience)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              className={css.saveExitBtn}
              disabled={isLoading}
            >
              Save & Exit (Complete Later)
            </button>
            <button
              type="submit"
              className={css.continueBtn}
              disabled={isLoading}
            >
              Save & Continue (To Step 3: Medical) →
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtWorkHistory;