"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useSchoolBusDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchSchoolBusDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  // FetchDriverDetailsResponse
} from "@/services/driverFormService";

import DateInput from "@/components/Common/DateInput/DateInput";
import Dropdown from "@/components/Common/Dropdown";

interface driverBusCertifications {
  id: string;
  
busIssuingBody:string;
  certificationName: string;
  dateIssued: Date | null;
  expirationDate: Date | null;
}

interface OtherCertification {
  id: string;
  certificationName: string;
  issuingBody: string;
  dateIssued: Date | null;
  expirationDate: Date | null;
}

interface MedicalFormValues {
  dotMedicalCardStatus: string;
  dotExpirationDate: Date | null;
  dotExaminerName: string;
  dotExaminerPhone: string;
  dotNationalRegistryNumber: string;
  dotRestriction: string;
  dotExemption: string;
  hasMedicalVariance: boolean;
  medicalVarianceDetails: string;
  holdOtherCertification: boolean;
  stateCertifications: driverBusCertifications[];
  otherCertifications: OtherCertification[];
}

// function isCertificationErrorArray(
//   value: unknown
// ): value is Array<FormikErrors<StateCertification | OtherCertification>> {
//   return Array.isArray(value);
// }

const Medical: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } =
    useSchoolBusDriverCategory();
  const router = useRouter();
  const [medicalStatusOptions, setMedicalStatusOptions] = useState<FormValue[]>(
    []
  );
  // const [states, setStates] = useState<State[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadAllData = async () => {
      try {
        const [formFields, detailsResponse] = await Promise.all([
          fetchSchoolBusDriverFormFields(),
         
          fetchDriverDetails(),
        ]);

        // Map API data to state and filter out empty options
        const medicalStatuses = (
          formFields["dot-medical-card-status-driver-cdl"] || []
        ).filter((option) => option.label?.en && option.label.en.trim() !== "");

        setMedicalStatusOptions(medicalStatuses);
        // setStates(stateList);

        // Pre-populate with existing data if available
        const driver = detailsResponse?.data?.driver;
        if (driver) {
          const cleanedValues: MedicalFormValues = {
            dotMedicalCardStatus: driver.dotMedicalCardStatus?.toString() || "",
            dotExpirationDate: driver.dotExpirationDate
              ? new Date(driver.dotExpirationDate)
              : null,
            dotExaminerName: driver.dotExaminerName || "",
            dotExaminerPhone: driver.dotExaminerPhone || "",
            dotNationalRegistryNumber: driver.dotNationalRegistryNumber || "",
            dotRestriction: driver.dotRestriction || "",
            dotExemption: driver.dotExemption || "",
            hasMedicalVariance: driver.holdBusCertification || false,
            medicalVarianceDetails: driver.medicalVarianceDetails || "",
            holdOtherCertification: driver.holdOtherCertification || false,
            // stateCertifications: [],
            otherCertifications: (driver.driverOtherCertifications || []).map(
              (cert, index) => ({
                id: `cert-${index}-${Date.now()}`,
                certificationName: cert.certificateName || "",
                issuingBody: cert.issuingBody || "",
                dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
                expirationDate: cert.expirationDate
                  ? new Date(cert.expirationDate)
                  : null,
              })
            ),
            stateCertifications: (driver.driverBusCertifications || []).map(
              (cert, index) => ({
                id: `bus-cert-${index}-${Date.now()}`,
                certificationName: cert.certificateName || "",
                busIssuingBody: cert.issuingBody || "", // assuming state is stored in `issuingBody`
                dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
                expirationDate: cert.expirationDate
                  ? new Date(cert.expirationDate)
                  : null,
             
              })
            ),
          };
          formik.setValues(cleanedValues);
        }
      } catch (err) {
        console.error("Failed to fetch data:", err);
        toast.error("Failed to load form data. Please refresh the page.");
      } finally {
        setIsLoading(false);
      }
    };

    loadAllData();
  }, []);

  const createEmptyStateCertification = (): driverBusCertifications => ({
    id: `state-cert-${Date.now()}-${Math.random()}`,
    busIssuingBody: "",
    certificationName: "",
    dateIssued: null,
    expirationDate: null,
  });

  const createEmptyOtherCertification = (): OtherCertification => ({
    id: `other-cert-${Date.now()}-${Math.random()}`,
    certificationName: "",
    issuingBody: "",
    dateIssued: null,
    expirationDate: null,
  });

  const initialValues: MedicalFormValues = {
    dotMedicalCardStatus: "",
    dotExpirationDate: null,
    dotExaminerName: "",
    dotExaminerPhone: "",
    dotNationalRegistryNumber: "",
    dotRestriction: "",
    dotExemption: "",
    hasMedicalVariance: false,
    medicalVarianceDetails: "",
    holdOtherCertification: false,
    stateCertifications: [],
    otherCertifications: [],
  };

  // Helper function to check if medical status requires expiration date
  const requiresExpirationDate = (statusId: string): boolean => {
    const option = medicalStatusOptions.find(
      (opt) => opt.formValueId.toString() === statusId
    );
    const label = option?.label.en.toLowerCase() || "";
    return (
      label.includes("current") ||
      label.includes("valid") ||
      label.includes("variance") ||
      label.includes("exemption")
    );
  };

  // Helper function to check if medical status is variance/exemption
  const isVarianceExemption = (statusId: string): boolean => {
    const option = medicalStatusOptions.find(
      (opt) => opt.formValueId.toString() === statusId
    );
    const label = option?.label.en.toLowerCase() || "";
    return label.includes("variance") || label.includes("exemption");
  };

  const createValidationSchema = () =>
    Yup.object().shape({
      dotMedicalCardStatus: Yup.string().required(
        "DOT Medical Card Status is required"
      ),
      dotExpirationDate: Yup.date()
        .nullable()
        .when("dotMedicalCardStatus", {
          is: (val: string) => requiresExpirationDate(val),
          then: (schema) =>
            schema.required("Expiration date is required for this status"),
          otherwise: (schema) => schema.notRequired(),
        }),
      medicalVarianceDetails: Yup.string().when("dotMedicalCardStatus", {
        is: (val: string) => isVarianceExemption(val),
        then: (schema) =>
          schema.required("Medical variance/exemption details are required"),
        otherwise: (schema) => schema.notRequired(),
      }),
      stateCertifications: Yup.array().of(
        Yup.object().shape({
          
          certificationName: Yup.string().required(
            "Certification name is required"
          ),
          expirationDate: Yup.date().nullable().notRequired(),
        })
      ),
      otherCertifications: Yup.array().of(
        Yup.object().shape({
          certificationName: Yup.string().required(
            "Certification name is required"
          ),
          expirationDate: Yup.date().nullable().notRequired(),
        })
      ),
    });

  const handleSubmit = async (
    values: MedicalFormValues,
    shouldContinue: boolean = true
  ) => {
    console.log("Medical form submission started with values:", values);

    setIsLoading(true);
    try {
      const payload = {
        currentStage: 3,
        currentStep: 3,
        driver: {
          dotMedicalCardStatus: values.dotMedicalCardStatus
            ? parseInt(values.dotMedicalCardStatus)
            : undefined,
          dotExpirationDate: values.dotExpirationDate
            ? values.dotExpirationDate.toISOString()
            : null,
          dotExaminerName: values.dotExaminerName || "",
          dotExaminerPhone: values.dotExaminerPhone || "",
          dotNationalRegistryNumber: values.dotNationalRegistryNumber || "",
          dotRestriction: values.dotRestriction || "",
          dotExemption: values.dotExemption || "",
          holdBusCertification: values.hasMedicalVariance,
          medicalVarianceDetails: values.medicalVarianceDetails || "",
          holdOtherCertification: values.holdOtherCertification,
          driverOtherCertifications: values.otherCertifications.map((cert) => ({
            certificateName: cert.certificationName,
            issuingBody: cert.issuingBody,
            dateIssued: cert.dateIssued ? cert.dateIssued.toISOString() : null,
            expirationDate: cert.expirationDate
              ? cert.expirationDate.toISOString()
              : null,
          })),
          driverBusCertifications: values.stateCertifications.map(
            (cert, index) => ({
              certificateName: cert.certificationName,
              issuingBody: cert.busIssuingBody, // assuming you're storing issuing body as state
              dateIssued: cert.dateIssued
                ? cert.dateIssued.toISOString()
                : null,
              expirationDate: cert.expirationDate
                ? cert.expirationDate.toISOString()
                : null,
              rank: index + 1,
            
            })
          ),
        },
      };

      console.log(
        "Submitting medical payload:",
        JSON.stringify(payload, null, 2)
      );
      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Medical information saved successfully!");
          window.scrollTo({ top: 0, behavior: "smooth" });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save medical information. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting medical information:", error);
      toast.error("Failed to save medical information. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik<MedicalFormValues>({
    initialValues,
    validationSchema: createValidationSchema(),
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  const addStateCertification = () => {
    const newCert = createEmptyStateCertification();
    formik.setFieldValue("stateCertifications", [
      ...formik.values.stateCertifications,
      newCert,
    ]);
  };

  const removeStateCertification = (index: number) => {
    const updated = [...formik.values.stateCertifications];
    updated.splice(index, 1);
    formik.setFieldValue("stateCertifications", updated);
  };

  const addOtherCertification = () => {
    const newCert = createEmptyOtherCertification();
    formik.setFieldValue("otherCertifications", [
      ...formik.values.otherCertifications,
      newCert,
    ]);
  };

  const removeOtherCertification = (index: number) => {
    const updated = [...formik.values.otherCertifications];
    updated.splice(index, 1);
    formik.setFieldValue("otherCertifications", updated);
  };

  if (isLoading) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div style={{ fontSize: "18px", color: "#666" }}>
          Loading medical information...
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: "2rem", maxWidth: "1200px", margin: "0 auto" }}>
      <div style={{ marginBottom: "2rem" }}>
        <h2
          style={{
            fontSize: "24px",
            fontWeight: "600",
            marginBottom: "1rem",
            color: "#333",
          }}
        >
          Step 3: Medical & Certifications (School Bus Driver)
        </h2>
        <p style={{ fontSize: "16px", color: "#666", marginBottom: "1rem" }}>
          Provide details about your DOT Medical Certification, state-specific
          requirements, and other relevant certifications.
        </p>
        <p style={{ fontSize: "14px", color: "#333" }}>
          <strong>Required fields are marked with *</strong>
        </p>
      </div>

      <form onSubmit={formik.handleSubmit}>
        {/* DOT Medical Information */}
        <div style={{ marginBottom: "2rem" }}>
          <h3
            style={{
              fontSize: "18px",
              fontWeight: "500",
              marginBottom: "1rem",
              color: "#333",
            }}
          >
            DOT Medical Information
          </h3>

          {/* DOT Medical Card Status */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "block",
                marginBottom: "0.5rem",
                fontWeight: "500",
                color: "#333",
              }}
            >
              DOT Medical Card Status: *
              <span
                style={{
                  fontSize: "12px",
                  color: "#666",
                  fontWeight: "normal",
                }}
              >
                {" "}
                (Required for drivers of vehicles designed to transport 16+
                passengers. See 49 CFR §391.41)
              </span>
            </label>
            <Dropdown
              options={medicalStatusOptions.map(option => ({ value: option.formValueId, label: option.label.en }))}
              value={formik.values.dotMedicalCardStatus}
              placeholder="Select Status"
              onChange={(value) => formik.setFieldValue("dotMedicalCardStatus", value)}
              error={formik.touched.dotMedicalCardStatus && formik.errors.dotMedicalCardStatus ? formik.errors.dotMedicalCardStatus : undefined}
              name="dotMedicalCardStatus"
            />
            {formik.touched.dotMedicalCardStatus &&
              formik.errors.dotMedicalCardStatus && (
                <div
                  style={{
                    color: "#dc3545",
                    fontSize: "14px",
                    marginTop: "0.25rem",
                  }}
                >
                  {formik.errors.dotMedicalCardStatus}
                </div>
              )}
          </div>

          {/* Conditional Fields based on Status */}
          {formik.values.dotMedicalCardStatus &&
            requiresExpirationDate(formik.values.dotMedicalCardStatus) && (
              <>
                {/* DOT Medical Card Expiration Date */}
                <div style={{ marginBottom: "1.5rem" }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: "0.5rem",
                      fontWeight: "500",
                      color: "#333",
                    }}
                  >
                    DOT Medical Card Expiration Date: *
                  </label>
                  <DateInput
                    selected={formik.values.dotExpirationDate}
                    onChange={(date) =>
                      formik.setFieldValue("dotExpirationDate", date)
                    }
                    placeholder="Select expiration date"
                  />
                  {formik.touched.dotExpirationDate &&
                    formik.errors.dotExpirationDate && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "0.25rem",
                        }}
                      >
                        {formik.errors.dotExpirationDate}
                      </div>
                    )}
                </div>

                {/* Medical Examiner Information */}
                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr",
                    gap: "1rem",
                    marginBottom: "1.5rem",
                  }}
                >
                  <div>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "0.5rem",
                        fontWeight: "500",
                        color: "#333",
                      }}
                    >
                      Medical Examiner&apos;s Name: (Optional)
                    </label>
                    <input
                      type="text"
                      name="dotExaminerName"
                      value={formik.values.dotExaminerName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter examiner name"
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "16px",
                      }}
                    />
                  </div>

                  <div>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "0.5rem",
                        fontWeight: "500",
                        color: "#333",
                      }}
                    >
                      Medical Examiner&apos;s Phone: (Optional)
                    </label>
                    <input
                      type="tel"
                      name="dotExaminerPhone"
                      value={formik.values.dotExaminerPhone}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="(*************"
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "16px",
                      }}
                    />
                  </div>
                </div>

                <div
                  style={{
                    display: "grid",
                    gridTemplateColumns: "1fr 1fr",
                    gap: "1rem",
                    marginBottom: "1.5rem",
                  }}
                >
                  <div>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "0.5rem",
                        fontWeight: "500",
                        color: "#333",
                      }}
                    >
                      National Registry Number (NRCME #): (Optional)
                    </label>
                    <input
                      type="text"
                      name="dotNationalRegistryNumber"
                      value={formik.values.dotNationalRegistryNumber}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter NRCME number"
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "16px",
                      }}
                    />
                  </div>

                  <div>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "0.5rem",
                        fontWeight: "500",
                        color: "#333",
                      }}
                    >
                      Medical Card Restrictions (if any): (Optional)
                    </label>
                    <input
                      type="text"
                      name="dotRestriction"
                      value={formik.values.dotRestriction}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter any restrictions"
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "16px",
                      }}
                    />
                  </div>
                </div>

                {/* Medical Variance/Exemption Details */}
                {isVarianceExemption(formik.values.dotMedicalCardStatus) && (
                  <div style={{ marginBottom: "1.5rem" }}>
                    <label
                      style={{
                        display: "block",
                        marginBottom: "0.5rem",
                        fontWeight: "500",
                        color: "#333",
                      }}
                    >
                      Type of Medical Variance/Exemption Document Carried: *
                    </label>
                    <input
                      type="text"
                      name="medicalVarianceDetails"
                      value={formik.values.medicalVarianceDetails}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="e.g., FMCSA Vision Exemption, Hearing SPE Certificate, Insulin Waiver"
                      style={{
                        width: "100%",
                        padding: "0.75rem",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "16px",
                      }}
                    />
                    {formik.touched.medicalVarianceDetails &&
                      formik.errors.medicalVarianceDetails && (
                        <div
                          style={{
                            color: "#dc3545",
                            fontSize: "14px",
                            marginTop: "0.25rem",
                          }}
                        >
                          {formik.errors.medicalVarianceDetails}
                        </div>
                      )}
                  </div>
                )}
              </>
            )}
        </div>

        {/* State-Specific School Bus Driver Certifications */}
        <div style={{ marginBottom: "2rem" }}>
          <h3
            style={{
              fontSize: "18px",
              fontWeight: "500",
              marginBottom: "1rem",
              color: "#333",
            }}
          >
            State-Specific School Bus Driver Certifications
            <span
              style={{ fontSize: "12px", color: "#666", fontWeight: "normal" }}
            >
              {" "}
              (Many states require specific training, physicals, or
              certifications beyond the standard CDL and DOT physical)
            </span>
          </h3>

          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "block",
                marginBottom: "0.5rem",
                fontWeight: "500",
                color: "#333",
              }}
            >
              Do you hold any state-specific School Bus Driver Certifications /
              Permits? *
            </label>
            <div style={{ display: "flex", gap: "1rem", marginTop: "0.5rem" }}>
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="hasMedicalVariance"
                  value="true"
                  checked={formik.values.hasMedicalVariance === true}
                  onChange={() =>
                    formik.setFieldValue("hasMedicalVariance", true)
                  }
                  style={{ marginRight: "0.5rem" }}
                />
                Yes
              </label>
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="hasMedicalVariance"
                  value="false"
                  checked={formik.values.hasMedicalVariance === false}
                  onChange={() =>
                    formik.setFieldValue("hasMedicalVariance", false)
                  }
                  style={{ marginRight: "0.5rem" }}
                />
                No
              </label>
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="hasMedicalVariance"
                  value="unsure"
                  checked={formik.values.hasMedicalVariance === null}
                  onChange={() =>
                    formik.setFieldValue("hasMedicalVariance", null)
                  }
                  style={{ marginRight: "0.5rem" }}
                />
                Unsure / Check State Requirements
              </label>
            </div>
          </div>

          {/* State Certifications Section */}
          {formik.values.hasMedicalVariance === true && (
            <div style={{ marginBottom: "1.5rem" }}>
              <p
                style={{
                  fontSize: "14px",
                  color: "#666",
                  marginBottom: "1rem",
                }}
              >
                List any state-specific certifications required in your
                operating area.
              </p>

              <button
                type="button"
                onClick={addStateCertification}
                style={{
                  padding: "0.75rem 1.5rem",
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                  fontSize: "16px",
                  marginBottom: "1rem",
                }}
              >
                + Add State Certification
              </button>

              {formik.values.stateCertifications.map((cert, index) => (
                <div
                  key={cert.id}
                  style={{
                    padding: "1.5rem",
                    border: "1px solid #e5e5e5",
                    borderRadius: "8px",
                    backgroundColor: "#fff",
                    marginBottom: "1rem",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginBottom: "1rem",
                    }}
                  >
                    <h4
                      style={{
                        fontSize: "16px",
                        fontWeight: "500",
                        color: "#333",
                        margin: "0",
                      }}
                    >
                      State Certification {index + 1}
                    </h4>
                    <button
                      type="button"
                      onClick={() => removeStateCertification(index)}
                      style={{
                        padding: "0.5rem 1rem",
                        backgroundColor: "#dc3545",
                        color: "white",
                        border: "none",
                        borderRadius: "4px",
                        cursor: "pointer",
                        fontSize: "14px",
                      }}
                    >
                      Remove This Certification
                    </button>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      gap: "1rem",
                      marginBottom: "1rem",
                    }}
                  >
                    
                    <div>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: "500",
                          color: "#333",
                        }}
                      >
                        Issuing Body / Provider: (Optional)
                      </label>
                      <input
                        type="text"
                        name={`stateCertifications[${index}].busIssuingBody`}
                        value={cert.busIssuingBody}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="e.g., American Heart Assoc., Red Cross"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          fontSize: "16px",
                        }}
                      />
                    </div>

                    <div>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: "500",
                          color: "#333",
                        }}
                      >
                        Certification Name / Type: *
                      </label>
                      <input
                        type="text"
                        name={`stateCertifications[${index}].certificationName`}
                        value={cert.certificationName}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="e.g., State DOE School Bus Driver Certificate"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          fontSize: "16px",
                        }}
                      />
                    </div>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      gap: "1rem",
                    }}
                  >
                    <div>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: "500",
                          color: "#333",
                        }}
                      >
                        Date Issued (Optional):
                      </label>
                      <DateInput
                        selected={cert.dateIssued}
                        onChange={(date) =>
                          formik.setFieldValue(
                            `stateCertifications[${index}].dateIssued`,
                            date
                          )
                        }
                        placeholder="Select issue date"
                      />
                    </div>

                    <div>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: "500",
                          color: "#333",
                        }}
                      >
                        Expiration Date (if applicable):
                      </label>
                      <DateInput
                        selected={cert.expirationDate}
                        onChange={(date) =>
                          formik.setFieldValue(
                            `stateCertifications[${index}].expirationDate`,
                            date
                          )
                        }
                        placeholder="Select expiration date"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Other Relevant Certifications */}
        <div style={{ marginBottom: "2rem" }}>
          <h3
            style={{
              fontSize: "18px",
              fontWeight: "500",
              marginBottom: "1rem",
              color: "#333",
            }}
          >
            Other Relevant Certifications
            <span
              style={{ fontSize: "12px", color: "#666", fontWeight: "normal" }}
            >
              {" "}
              (Optional - e.g., First Aid, CPR, Passenger Assistance)
            </span>
          </h3>

          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "block",
                marginBottom: "0.5rem",
                fontWeight: "500",
                color: "#333",
              }}
            >
              Do you hold certifications like First Aid, CPR, or Passenger
              Assistance Training (PATS)?
            </label>
            <div style={{ display: "flex", gap: "1rem", marginTop: "0.5rem" }}>
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="holdOtherCertification"
                  value="true"
                  checked={formik.values.holdOtherCertification === true}
                  onChange={() =>
                    formik.setFieldValue("holdOtherCertification", true)
                  }
                  style={{ marginRight: "0.5rem" }}
                />
                Yes
              </label>
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="holdOtherCertification"
                  value="false"
                  checked={formik.values.holdOtherCertification === false}
                  onChange={() =>
                    formik.setFieldValue("holdOtherCertification", false)
                  }
                  style={{ marginRight: "0.5rem" }}
                />
                No
              </label>
            </div>
          </div>

          {/* Other Certifications Section */}
          {formik.values.holdOtherCertification === true && (
            <div style={{ marginBottom: "1.5rem" }}>
              <p
                style={{
                  fontSize: "14px",
                  color: "#666",
                  marginBottom: "1rem",
                }}
              >
                List relevant certifications below.
              </p>

              <button
                type="button"
                onClick={addOtherCertification}
                style={{
                  padding: "0.75rem 1.5rem",
                  backgroundColor: "#28a745",
                  color: "white",
                  border: "none",
                  borderRadius: "4px",
                  cursor: "pointer",
                  fontSize: "16px",
                  marginBottom: "1rem",
                }}
              >
                + Add Certification
              </button>

              {formik.values.otherCertifications.map((cert, index) => (
                <div
                  key={cert.id}
                  style={{
                    padding: "1.5rem",
                    border: "1px solid #e5e5e5",
                    borderRadius: "8px",
                    backgroundColor: "#fff",
                    marginBottom: "1rem",
                  }}
                >
                  <div
                    style={{
                      display: "flex",
                      justifyContent: "space-between",
                      alignItems: "center",
                      marginBottom: "1rem",
                    }}
                  >
                    <h4
                      style={{
                        fontSize: "16px",
                        fontWeight: "500",
                        color: "#333",
                        margin: "0",
                      }}
                    >
                      Certification {index + 1}
                    </h4>
                    <button
                      type="button"
                      onClick={() => removeOtherCertification(index)}
                      style={{
                        padding: "0.5rem 1rem",
                        backgroundColor: "#dc3545",
                        color: "white",
                        border: "none",
                        borderRadius: "4px",
                        cursor: "pointer",
                        fontSize: "14px",
                      }}
                    >
                      Remove This Certification
                    </button>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      gap: "1rem",
                      marginBottom: "1rem",
                    }}
                  >
                    <div>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: "500",
                          color: "#333",
                        }}
                      >
                        Certification Name / Type: *
                      </label>
                      <input
                        type="text"
                        name={`otherCertifications[${index}].certificationName`}
                        value={cert.certificationName}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="e.g., CPR/AED Certification, Basic First Aid"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          fontSize: "16px",
                        }}
                      />
                    </div>

                    <div>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: "500",
                          color: "#333",
                        }}
                      >
                        Issuing Body / Provider: (Optional)
                      </label>
                      <input
                        type="text"
                        name={`otherCertifications[${index}].issuingBody`}
                        value={cert.issuingBody}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="e.g., American Heart Assoc., Red Cross"
                        style={{
                          width: "100%",
                          padding: "0.75rem",
                          border: "1px solid #ccc",
                          borderRadius: "4px",
                          fontSize: "16px",
                        }}
                      />
                    </div>
                  </div>

                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns: "1fr 1fr",
                      gap: "1rem",
                    }}
                  >
                    <div>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: "500",
                          color: "#333",
                        }}
                      >
                        Date Issued (Optional):
                      </label>
                      <DateInput
                        selected={cert.dateIssued}
                        onChange={(date) =>
                          formik.setFieldValue(
                            `otherCertifications[${index}].dateIssued`,
                            date
                          )
                        }
                        placeholder="Select issue date"
                      />
                    </div>

                    <div>
                      <label
                        style={{
                          display: "block",
                          marginBottom: "0.5rem",
                          fontWeight: "500",
                          color: "#333",
                        }}
                      >
                        Expiration Date (if applicable):
                      </label>
                      <DateInput
                        selected={cert.expirationDate}
                        onChange={(date) =>
                          formik.setFieldValue(
                            `otherCertifications[${index}].expirationDate`,
                            date
                          )
                        }
                        placeholder="Select expiration date"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: "2rem",
            padding: "1.5rem",
            borderTop: "1px solid #e5e5e5",
          }}
        >
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              &lt; Back (To Step 2: History)
            </button>
          )}

          <div style={{ display: "flex", gap: "1rem" }}>
            <button
              type="submit"
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              {isLoading ? "Saving..." : "Save & Continue (To Step 4: Docs) >"}
            </button>

            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              Save & Exit (Complete Later)
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default Medical;
