"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  fetchNemtDriverFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
} from "@/services/driverFormService";
import CheckboxGroup from "@/components/Common/CheckboxGroup";
import DateInput from "@/components/Common/DateInput/DateInput";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtConsents.module.scss';

interface ConsentsFormValues {
  profileSharingConsent: boolean;
  backgroundCheckConsent: boolean;
  certificationOfTruthfulness: boolean;
  termsPrivacyAgreement: boolean;
  dateAvailable: string;
  specificDate: Date | null;
  preferredEmploymentTypes: number[];
  preferredShifts: number[];
  geographicArea: string;
}

const NemtConsents: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [employmentTypeOptions, setEmploymentTypeOptions] = useState<FormValue[]>([]);
  const [shiftOptions, setShiftOptions] = useState<FormValue[]>([]);
  const [driverData, setDriverData] = useState<any>(null);
  const [availabilityOptions] = useState([
    { value: "immediately", label: "Immediately Available" },
    { value: "1-week", label: "Available within 1 Week" },
    { value: "2-weeks", label: "Available within 2 Weeks" },
    { value: "1-month", label: "Available within 1 Month" },
    { value: "specific-date", label: "Specific Date" },
    { value: "currently-employed", label: "Currently Employed/Contracted - Seeking Opportunities" },
  ]);

  const validationSchema = Yup.object({
    profileSharingConsent: Yup.boolean().oneOf([true], "Profile sharing consent is required"),
    backgroundCheckConsent: Yup.boolean().oneOf([true], "Background check consent is required"),
    certificationOfTruthfulness: Yup.boolean().oneOf([true], "Certification of truthfulness is required"),
    termsPrivacyAgreement: Yup.boolean().oneOf([true], "Terms & Privacy agreement is required"),
    dateAvailable: Yup.string().required("Date available is required"),
    specificDate: Yup.date().when("dateAvailable", {
      is: "specific-date",
      then: (schema) => schema.required("Specific date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    preferredEmploymentTypes: Yup.array()
      .of(Yup.number())
      .min(1, "At least one employment type is required"),
    preferredShifts: Yup.array().of(Yup.number()),
    geographicArea: Yup.string(),
  });

  const formik = useFormik<ConsentsFormValues>({
    initialValues: {
      profileSharingConsent: false,
      backgroundCheckConsent: false,
      certificationOfTruthfulness: false,
      termsPrivacyAgreement: false,
      dateAvailable: "",
      specificDate: null,
      preferredEmploymentTypes: [],
      preferredShifts: [],
      geographicArea: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load form field options
        const formFieldsResponse = await fetchNemtDriverFormFields([
          "preferred-employment-types-driver-cdl",
          "preferred-shifts-driver-non-emergency-medical-transportation",
        ]);

        if (formFieldsResponse?.status && formFieldsResponse?.data) {
          const fields = formFieldsResponse.data;

          const employmentTypeKey = "preferred-employment-types-driver-cdl";
          if (fields[employmentTypeKey]?.formValues) {
            setEmploymentTypeOptions(fields[employmentTypeKey].formValues);
          }

          const shiftsKey = "preferred-shifts-driver-non-emergency-medical-transportation";
          if (fields[shiftsKey]?.formValues) {
            setShiftOptions(fields[shiftsKey].formValues);
          }
        }

        // Load existing driver data
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver;
          setDriverData(driver); // Store driver data for review section

          const consents = (driver as any).nemtConsents;

          if (consents) {
            formik.setValues({
              profileSharingConsent: consents.profileSharingConsent || false,
              backgroundCheckConsent: consents.backgroundCheckConsent || false,
              certificationOfTruthfulness: consents.certificationOfTruthfulness || false,
              termsPrivacyAgreement: consents.termsPrivacyAgreement || false,
              dateAvailable: consents.dateAvailable || "",
              specificDate: consents.specificDate ? new Date(consents.specificDate) : null,
              preferredEmploymentTypes: consents.preferredEmploymentTypes || [],
              preferredShifts: consents.preferredShifts || [],
              geographicArea: consents.geographicArea || "",
            });
          }
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load data. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (values: ConsentsFormValues) => {
    try {
      setIsLoading(true);

      const payload = {
        nemtConsents: {
          profileSharingConsent: values.profileSharingConsent,
          backgroundCheckConsent: values.backgroundCheckConsent,
          certificationOfTruthfulness: values.certificationOfTruthfulness,
          termsPrivacyAgreement: values.termsPrivacyAgreement,
          dateAvailable: values.dateAvailable,
          specificDate: values.specificDate?.toISOString(),
          preferredEmploymentTypes: values.preferredEmploymentTypes,
          preferredShifts: values.preferredShifts,
          geographicArea: values.geographicArea,
        },
        profileCompleted: true, // Mark profile as completed
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        toast.success("NEMT Driver profile completed successfully!");
        // Redirect to home or success page
        setTimeout(() => {
          router.push("/");
        }, 2000);
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to complete profile. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error completing profile:", error);
      toast.error("Failed to complete profile. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions to format review data
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not provided";
    return new Date(dateString).toLocaleDateString();
  };

  const getExperienceData = () => {
    const experience = driverData?.nemtExperience;
    if (!experience) {
      return {
        nemtExp: "Data not available",
        drivingExp: "Data not available",
        vehicles: "Data not available",
        passengers: "Data not available",
        skills: "Data not available",
      };
    }

    return {
      nemtExp: experience.nemtExperience || "Not specified",
      drivingExp: experience.totalDrivingExperience || "Not specified",
      vehicles: experience.vehicleTypes?.length ? "Vehicle types selected" : "Not specified",
      passengers: experience.passengerTypes?.length ? "Passenger types selected" : "Not specified",
      skills: experience.assistanceSkills?.length ? "Skills selected" : "Not specified",
    };
  };

  const getMedicalData = () => {
    const medical = driverData?.nemtMedical;
    if (!medical) {
      return {
        dotRequired: "Data not available",
        cpr: "Data not available",
        firstAid: "Data not available",
        pats: "Data not available",
        mavt: "Data not available",
      };
    }

    return {
      dotRequired: medical.dotMedicalRequired || "Not specified",
      cpr: medical.cprCertified === "Yes" ? `Yes, Exp: ${formatDate(medical.cprExpiration)}` : "No",
      firstAid: medical.firstAidCertified === "Yes" ? `Yes, Exp: ${formatDate(medical.firstAidExpiration)}` : "No",
      pats: medical.patsCertified || "Not specified",
      mavt: medical.mavtCertified || "Not specified",
    };
  };

  const getDocumentData = () => {
    const documents = driverData?.nemtDocuments;
    if (!documents) {
      return {
        cprCard: "Data not available",
        firstAidCard: "Data not available",
        patsCert: "Data not available",
        mavtCert: "Data not available",
      };
    }

    return {
      cprCard: documents.cprCertificationFiles?.length ? "✓" : "Not uploaded",
      firstAidCard: documents.firstAidCertificationFiles?.length ? "✓" : "Not uploaded",
      patsCert: documents.patsCertificationFiles?.length ? "✓" : "Not uploaded",
      mavtCert: documents.mavtCertificationFiles?.length ? "✓" : "Not uploaded",
    };
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.consents}>
      <h3>Step 5: Consents, Availability & Final Review (NEMT Driver)</h3>
      <p>Final step! Review your NEMT Driver profile, agree to consents, and specify your availability.</p>

      <form onSubmit={formik.handleSubmit}>
        {/* Required Consents & Agreements */}
        <div className={css.section}>
          <h4>Required Consents & Agreements</h4>
          <p className={css.requiredNote}>(All checkboxes below are required to complete your profile *)</p>

          <div className={css.consentItem}>
            <label className={css.checkboxLabel}>
              <input
                type="checkbox"
                name="profileSharingConsent"
                checked={formik.values.profileSharingConsent}
                onChange={formik.handleChange}
                className={css.checkbox}
              />
              <span className={css.checkboxText}>
                <strong>Profile Sharing Consent:</strong> I consent to allow DriverJobz to share my profile information (excluding full sensitive numbers like SSN/License# until Hiring Packet approval) and indicate document upload status with registered NEMT providers/brokers on this platform for employment/contracting consideration. I understand I control full document/detail release via Hiring Packet requests.
              </span>
            </label>
            {formik.touched.profileSharingConsent && formik.errors.profileSharingConsent && (
              <p className={css.error}>{formik.errors.profileSharingConsent}</p>
            )}
          </div>

          <div className={css.consentItem}>
            <label className={css.checkboxLabel}>
              <input
                type="checkbox"
                name="backgroundCheckConsent"
                checked={formik.values.backgroundCheckConsent}
                onChange={formik.handleChange}
                className={css.checkbox}
              />
              <span className={css.checkboxText}>
                <strong>Background Check Consent:</strong> I understand that potential employers/brokers will conduct background checks as a condition of engagement. This typically includes Motor Vehicle Record (MVR) checks, criminal background checks (often required for transporting vulnerable populations), and potentially drug screening per company/contract policy. I consent to these checks being performed by companies/brokers I connect with or apply to via DriverJobz.
              </span>
            </label>
            {formik.touched.backgroundCheckConsent && formik.errors.backgroundCheckConsent && (
              <p className={css.error}>{formik.errors.backgroundCheckConsent}</p>
            )}
          </div>

          <div className={css.consentItem}>
            <label className={css.checkboxLabel}>
              <input
                type="checkbox"
                name="certificationOfTruthfulness"
                checked={formik.values.certificationOfTruthfulness}
                onChange={formik.handleChange}
                className={css.checkbox}
              />
              <span className={css.checkboxText}>
                <strong>Certification of Truthfulness:</strong> I certify that all information provided in this application profile is true, accurate, and complete to the best of my knowledge. I understand that any misrepresentation, falsification, or omission may result in disqualification from opportunities or termination/contract cancellation.
              </span>
            </label>
            {formik.touched.certificationOfTruthfulness && formik.errors.certificationOfTruthfulness && (
              <p className={css.error}>{formik.errors.certificationOfTruthfulness}</p>
            )}
          </div>

          <div className={css.consentItem}>
            <label className={css.checkboxLabel}>
              <input
                type="checkbox"
                name="termsPrivacyAgreement"
                checked={formik.values.termsPrivacyAgreement}
                onChange={formik.handleChange}
                className={css.checkbox}
              />
              <span className={css.checkboxText}>
                <strong>Terms & Privacy Agreement:</strong> I acknowledge that I have read and agree to the DriverJobz <a href="/terms" target="_blank">Terms of Service</a> and <a href="/privacy" target="_blank">Privacy Policy</a>.
              </span>
            </label>
            {formik.touched.termsPrivacyAgreement && formik.errors.termsPrivacyAgreement && (
              <p className={css.error}>{formik.errors.termsPrivacyAgreement}</p>
            )}
          </div>
        </div>

        {/* Availability Information */}
        <div className={css.section}>
          <h4>Availability Information</h4>

          <div className={css.formRow}>
            <label>Date Available for Work: *</label>
            <Dropdown
              options={availabilityOptions.map(option => ({ value: option.value, label: option.label }))}
              value={formik.values.dateAvailable}
              placeholder="Select Availability"
              onChange={(value) => formik.setFieldValue("dateAvailable", value)}
              error={formik.touched.dateAvailable && formik.errors.dateAvailable ? formik.errors.dateAvailable : undefined}
              name="dateAvailable"
            />
          </div>

          {formik.values.dateAvailable === "specific-date" && (
            <div className={css.formRow}>
              <label>Specific Date: *</label>
              <DateInput
                selected={formik.values.specificDate}
                onChange={(date) => formik.setFieldValue("specificDate", date)}
              />
              {formik.touched.specificDate && formik.errors.specificDate && (
                <p className={css.error}>{formik.errors.specificDate}</p>
              )}
            </div>
          )}

          <div className={css.formRow}>
            <label>Preferred Employment Type(s): * (Check all that apply)</label>
            {employmentTypeOptions.length > 0 ? (
              <CheckboxGroup
                name="preferredEmploymentTypes"
                options={employmentTypeOptions.map(option => ({
                  id: option.formValueId,
                  label: option.label.en
                }))}
                selectedValues={formik.values.preferredEmploymentTypes}
                onChange={(selected) => {
                  formik.setFieldValue('preferredEmploymentTypes', selected);
                }}
              />
            ) : (
              <div>Loading employment type options...</div>
            )}
            {formik.touched.preferredEmploymentTypes && formik.errors.preferredEmploymentTypes && (
              <p className={css.error}>{formik.errors.preferredEmploymentTypes}</p>
            )}
          </div>

          <div className={css.formRow}>
            <label>Preferred Shift(s): (Optional - Check all that apply)</label>
            {shiftOptions.length > 0 ? (
              <CheckboxGroup
                name="preferredShifts"
                options={shiftOptions.map(option => ({
                  id: option.formValueId,
                  label: option.label.en
                }))}
                selectedValues={formik.values.preferredShifts}
                onChange={(selected) => {
                  formik.setFieldValue('preferredShifts', selected);
                }}
              />
            ) : (
              <div>Loading shift options...</div>
            )}
          </div>

          <div className={css.formRow}>
            <label>Geographic Area Willing to Cover: (Optional)</label>
            <input
              type="text"
              name="geographicArea"
              value={formik.values.geographicArea}
              onChange={formik.handleChange}
              placeholder="e.g., Within 30 miles of Anytown, All of County X"
              className={css.input}
            />
          </div>
        </div>

        {/* Final Review */}
        <div className={css.section}>
          <h4>Final Review</h4>
          <p>Please carefully review all the information you've provided below. Use the "Edit Section" links to make corrections.</p>

          <div className={css.reviewSection}>
            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 2: Essentials</strong>
                <a href="/category/driver" className={css.editLink}>[Edit Stage 2 Info]</a>
              </div>
              <div className={css.reviewValue}>
                <ul>
                  <li>Category: [NEMT Driver]</li>
                  <li>License: [Standard Class C, NY, Exp: MM/DD/YYYY]</li>
                  <li>Endorsements: [N/A] | Restrictions: [None]</li>
                  <li>Safety Summary: Accidents [0], Violations [0], Suspension [No]</li>
                  <li>ID/License Upload: [✓]</li>
                </ul>
              </div>
            </div>

            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 3 - Step 1: Experience</strong>
                <button
                  type="button"
                  onClick={() => router.push('/category/nemt-driver?step=1')}
                  className={css.editLink}
                >
                  [Edit Experience]
                </button>
              </div>
              <div className={css.reviewValue}>
                {driverData ? (
                  <ul>
                    <li>NEMT Exp: [{getExperienceData().nemtExp}] | Driving Exp: [{getExperienceData().drivingExp}]</li>
                    <li>Vehicles: [{getExperienceData().vehicles}]</li>
                    <li>Passengers: [{getExperienceData().passengers}] | Skills: [{getExperienceData().skills}]</li>
                    <li>Brokers: [Data from experience form]</li>
                  </ul>
                ) : (
                  <p>Loading experience data...</p>
                )}
              </div>
            </div>

            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 3 - Step 2: History</strong>
                <button
                  type="button"
                  onClick={() => router.push('/category/nemt-driver?step=2')}
                  className={css.editLink}
                >
                  [Edit History]
                </button>
              </div>
              <div className={css.reviewValue}>
                {driverData ? (
                  <ul>
                    <li>Periods Reported: [{driverData.driverEmploymentHistory?.length || 0}] (Covering employment history)</li>
                    <li><em>Employment history data available</em></li>
                  </ul>
                ) : (
                  <p>Loading history data...</p>
                )}
              </div>
            </div>

            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 3 - Step 3: Medical</strong>
                <button
                  type="button"
                  onClick={() => router.push('/category/nemt-driver?step=3')}
                  className={css.editLink}
                >
                  [Edit Medical]
                </button>
              </div>
              <div className={css.reviewValue}>
                {driverData ? (
                  <ul>
                    <li>DOT Med Required?: [{getMedicalData().dotRequired}]</li>
                    <li>CPR: [{getMedicalData().cpr}] | First Aid: [{getMedicalData().firstAid}]</li>
                    <li>PATS: [{getMedicalData().pats}] | MAVT/MAVO: [{getMedicalData().mavt}]</li>
                  </ul>
                ) : (
                  <p>Loading medical data...</p>
                )}
              </div>
            </div>

            <div className={css.reviewItem}>
              <div className={css.reviewLabel}>
                <strong>Stage 3 - Step 4: Documents</strong>
                <button
                  type="button"
                  onClick={() => router.push('/category/nemt-driver?step=4')}
                  className={css.editLink}
                >
                  [Edit Documents]
                </button>
              </div>
              <div className={css.reviewValue}>
                {driverData ? (
                  <ul>
                    <li>CPR Card: [{getDocumentData().cprCard}] | First Aid Card: [{getDocumentData().firstAidCard}]</li>
                    <li>PATS Cert: [{getDocumentData().patsCert}] | MAVT/MAVO Cert: [{getDocumentData().mavtCert}]</li>
                  </ul>
                ) : (
                  <p>Loading document data...</p>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Submit Profile Section */}
        <div className={css.section}>
          <h4>Submit Your Full NEMT Driver Profile</h4>
          <p>By clicking "Complete Profile", you confirm your review, accuracy, and agreement to the consents. This enables 1-Click Apply and allows connected providers/brokers to request your full hiring docket.</p>
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 4: Documents)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="submit"
              className={css.completeBtn}
              disabled={isLoading}
            >
              Complete Full Profile & Activate Features
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtConsents;