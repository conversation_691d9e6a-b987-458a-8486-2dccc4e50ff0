.consents {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }

  > p {
    font-size: 16px;
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.5;
  }

  .section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;

    h4 {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #333;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }
  }

  .requiredNote {
    font-size: 14px;
    color: #dc3545;
    font-weight: 500;
    margin-bottom: 1.5rem;
    font-style: italic;
  }

  // Consent checkboxes
  .consentItem {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    transition: border-color 0.2s;

    &:hover {
      border-color: #007bff;
    }
  }

  .checkboxLabel {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    line-height: 1.5;
  }

  .checkbox {
    margin-right: 1rem;
    margin-top: 0.25rem;
    width: 18px;
    height: 18px;
    flex-shrink: 0;
    cursor: pointer;
  }

  .checkboxText {
    flex: 1;
    font-size: 15px;
    color: #333;
    line-height: 1.6;

    strong {
      color: #007bff;
      font-weight: 600;
    }

    a {
      color: #007bff;
      text-decoration: underline;

      &:hover {
        color: #0056b3;
      }
    }
  }

  // Form elements
  .formRow {
    margin-bottom: 2rem;

    label {
      display: block;
      font-weight: 500;
      margin-bottom: 0.75rem;
      color: #333;
      line-height: 1.4;

      sup {
        color: #dc3545;
        font-weight: bold;
      }
    }
  }

  .select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    background-color: white;
    cursor: pointer;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
  }

  .input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    &::placeholder {
      color: #999;
    }
  }

  .error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 0.5rem;
    font-weight: 500;
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 18px;
    color: #666;
  }

  // Navigation buttons
  .navigationButtons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #ddd;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }
  }

  .backBtn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
    font-size: 16px;

    &:hover {
      background-color: #545b62;
    }

    &:disabled {
      background-color: #adb5bd;
      cursor: not-allowed;
    }
  }

  .rightButtons {
    display: flex;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }

  .completeBtn {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s;
    font-size: 18px;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);

    &:hover {
      background: linear-gradient(135deg, #218838, #1ea085);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
    }

    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }
  }

  // Checkbox group styling
  .checkboxGroup {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;

    .checkboxOption {
      display: flex;
      align-items: center;
      padding: 0.5rem;
      border-radius: 4px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f8f9fa;
      }

      input[type="checkbox"] {
        margin-right: 0.75rem;
        width: 16px;
        height: 16px;
      }

      label {
        margin: 0;
        cursor: pointer;
        font-weight: normal;
      }
    }
  }

  // Date input styling
  .dateInput {
    .react-datepicker-wrapper {
      width: 100%;
    }

    .react-datepicker__input-container input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;

      &:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;

    .section {
      padding: 1rem;
    }

    h3 {
      font-size: 20px;
    }

    .section h4 {
      font-size: 18px;
    }

    .consentItem {
      padding: 1rem;
    }

    .checkboxText {
      font-size: 14px;
    }

    .completeBtn {
      font-size: 16px;
      padding: 0.75rem 1.5rem;
    }
  }

  // Final review section styling
  .reviewSection {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 0;
    margin-top: 1.5rem;
  }

  .reviewItem {
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;

    &:last-child {
      border-bottom: none;
    }

    .reviewLabel {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      strong {
        font-size: 16px;
        color: #333;
      }
    }

    .reviewValue {
      ul {
        margin: 0;
        padding-left: 1.5rem;
        list-style-type: disc;

        li {
          margin-bottom: 0.5rem;
          color: #666;
          line-height: 1.4;

          &:last-child {
            margin-bottom: 0;
          }

          em {
            color: #888;
            font-style: italic;
          }
        }
      }
    }

    .editLink {
      background: none;
      border: none;
      color: #007bff;
      text-decoration: none;
      font-size: 14px;
      cursor: pointer;
      padding: 0;

      &:hover {
        text-decoration: underline;
        color: #0056b3;
      }
    }
  }
}
