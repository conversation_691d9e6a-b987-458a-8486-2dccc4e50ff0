"use client"
import CheckboxGroup from "@/components/Common/CheckboxGroup";
import Dropdown from "@/components/Common/Dropdown";
import {
  fetchDriverDetails,
  fetchSchoolBusAideFormFields,
  FormValue,
  submitDriverDetails,
  FetchDriverDetailsResponse
} from "@/services/driverFormService";
import { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
// import { useRouter } from "next/navigation";
import { useSchoolBusAideCategory } from "@/contexts/CommonDriverCategoryContext";
import { useRouter } from "next/navigation";
 

interface FormValues {
  schoolBusExperience: string;
  ageGroup: number[];
  specialNeed: number[];
  supervisionSafety: number[];
  assistanceCare: number[];
  otherOptions: number[];
  physicalAbility: string;
  assistanceSkills?: number[];
  supervisionSkills?: number[];
  otherSkills?: number[];
  specialNeedTransported?: number[];
  ageGroupTransported?: number[];
  totalVerifiableRelevantExperience?: number;
}
type DriverDetails = FetchDriverDetailsResponse["data"]["driver"] & FormValues;

const AideExperience: React.FC = () => {
  const [ageOptions, setAgeOptions] = useState<FormValue[]>([]);
  const [assistanceCare, setAssistanceCare] = useState<FormValue[]>([]);
  const [otherOptions, setOtherOptions] = useState<FormValue[]>([]);
  const [supervisionSafety, setSupervisionsSafety] = useState<FormValue[]>([]);
  const [physicalAbility, setPhysicalAbility] = useState<FormValue[]>([]);
  const [specialNeedsOptions, setSpecialNeedsOptions] = useState<FormValue[]>([]);
    const { updateStepFromApiResponse } = useSchoolBusAideCategory();
      const router = useRouter();
      // const [isLoading, setIsLoading] = useState(false);

  const experienceYears = [
    "< 1 Year", "1 Year", "2 Years", "3 Years", "4 Years",
    "5 Years", "6 - 10 Years", "10+ Years",
  ];

  const formik = useFormik<FormValues>({
    initialValues: {
      schoolBusExperience: "",
      ageGroup: [],
      specialNeed: [],
      supervisionSafety: [],
      assistanceCare: [],
      otherOptions: [],
      physicalAbility:  "",
    },
    validationSchema: Yup.object({
      schoolBusExperience: Yup.string().required("Experience is required"),
      ageGroup: Yup.array().min(1, "Select at least one age group"),
      specialNeed: Yup.array().min(1, "Select at least one special need"),
      supervisionSafety: Yup.array().min(1, "Select at least one supervision skill"),
      assistanceCare: Yup.array().min(1, "Select at least one assistance skill"),
      otherOptions: Yup.array().min(1, "Select at least one other responsibility"),
      physicalAbility: Yup.string().required("Physical ability is required"),
    }),
    onSubmit: async (values) => {
     await handleSubmit(values,true)
     
    },
  });

 
 const handleSubmit= async(values:FormValues ,shouldContinue: boolean=true)=>{

 try{
     const mapExperienceToYears = (exp: string): number => {
        if (exp === "< 1 Year") return 0;
        if (exp === "10+ Years") return 10;
        const parsed = parseInt(exp.split(" ")[0], 10);
        return isNaN(parsed) ? 0 : parsed;
      };

      const payload = {
        currentStage: 3,
        currentStep: 1,
        driver: {
          totalVerifiableRelevantExperience: mapExperienceToYears(values.schoolBusExperience),
          ageGroupTransported: values.ageGroup,
          specialNeedTransported: values.specialNeed,
          supervisionSkills: values.supervisionSafety,
          assistanceSkills: values.assistanceCare,
          otherSkills: values.otherOptions,
          physicalAbility: [Number(values.physicalAbility)],
        },
      };

     const response= await submitDriverDetails(payload)
     if(response && response?.status){
      if(shouldContinue){
       updateStepFromApiResponse(response)
        toast.success('Submit Successfully')
        // window.scrollTo({ top: 0, behavior: 'smooth' });
      }else{
       toast.success("Draft saved successfully! Redirecting to home...");
       setTimeout(() => {
         router.push("/")
       }, 1500);
      }
      
     }else{
       toast.error("Failed to save Experience. Please try again.")
      }
 }catch(error)
 {
 console.log(error)
 toast.error("Failed to save Experience. Please try again.") 
 }
 }
  useEffect(() => {
    const loadData = async () => {
      try {
        const [formFieldData, existingData] = await Promise.all([
          fetchSchoolBusAideFormFields(),
          fetchDriverDetails(),
        ]);

        setAgeOptions(formFieldData["age-groups-transported-driver-school-bus-driver"] || []);
        setSpecialNeedsOptions(formFieldData["special-needs-transport-experience-driver-school-bus-driver"] || []);
        setPhysicalAbility(formFieldData["physical-ability-driver-bus-aide-assistant"] || []);
        setOtherOptions(formFieldData["other-driver-bus-aide-assistant"] || []);
        setAssistanceCare(formFieldData["assistance-care-driver-bus-aide-assistant"] || []);
        setSupervisionsSafety(formFieldData["supervision-safety-responsibilities-driver-bus-aide-assistant"] || []);

        const details = existingData?.data?.driver as DriverDetails;
        if (details) {
          const reverseMapExperience: Record<number, string> = {
            0: "< 1 Year",
            1: "1 Year",
            2: "2 Years",
            3: "3 Years",
            4: "4 Years",
            5: "5 Years",
            6: "6 - 10 Years",
            10: "10+ Years",
          };

          formik.setValues({
            schoolBusExperience: reverseMapExperience[details.totalVerifiableRelevantExperience ?? 0] || "",
            ageGroup: details.ageGroupTransported || [],
            specialNeed: details.specialNeedTransported || [],
            supervisionSafety: details.supervisionSkills || [],
            assistanceCare: details.assistanceSkills || [],
            otherOptions: details.otherSkills || [],
            physicalAbility: (details.physicalAbility?.[0]?.toString() || "")
          });
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load aide form data");
      }
    };

    loadData();
  }, []);

  
  return (
    <form
      onSubmit={formik.handleSubmit}
      style={{ padding: "2rem", maxWidth: "1200px", margin: "0 auto" }}
    >
      {/* Bus Aide Experience */}
      <div
        style={{
          padding: "1.5rem",
          border: "1px solid #e5e5e5",
          borderRadius: "8px",
          backgroundColor: "#fff",
        }}
      >
        <h3
          style={{
            fontSize: "20px",
            fontWeight: "500",
            marginBottom: "1.5rem",
            color: "#333",
          }}
        >
          Bus Aide / Assistant Experience
        </h3>

        <div style={{ marginBottom: "1.5rem" }}>
          <label
            htmlFor="schoolBusExperience"
            style={{
              display: "block",
              marginBottom: "0.5rem",
              fontWeight: "500",
              color: "#333",
            }}
          >
            Total Years of Verifiable Experience *
          </label>
          <Dropdown
            options={experienceYears.map(year => ({ value: year, label: year }))}
            value={formik.values.schoolBusExperience}
            placeholder="Select Years"
            onChange={(value) => formik.setFieldValue("schoolBusExperience", value)}
            error={formik.touched.schoolBusExperience && formik.errors.schoolBusExperience ? formik.errors.schoolBusExperience : undefined}
            name="schoolBusExperience"
          />
          {formik.touched.schoolBusExperience &&
            formik.errors.schoolBusExperience && (
              <div style={{ color: "red" }}>
                {formik.errors.schoolBusExperience}
              </div>
            )}
        </div>
      </div>

      {/* Student Population Experience */}
      <div
        style={{
          padding: "1.5rem",
          border: "1px solid #e5e5e5",
          borderRadius: "8px",
          backgroundColor: "#fff",
        }}
      >
        <h3>Student Population Experience *</h3>
        <p style={{ marginBottom: "1.5rem" }}>(Check all that apply)</p>

        <h3>Age Groups Assisted</h3>
        <CheckboxGroup
          name="ageGroup"
          options={ageOptions.map((item) => ({
            id: item.formValueId,
            label: item.label.en,
          }))}
          selectedValues={formik.values.ageGroup}
          onChange={(val) => formik.setFieldValue("ageGroup", val)}
        />
        {formik.touched.ageGroup && formik.errors.ageGroup && (
          <div style={{ color: "red" }}>{formik.errors.ageGroup}</div>
        )}

        <h3 style={{ marginTop: "2rem" }}>
          Experience with Specific Student Needs
        </h3>
        <CheckboxGroup
          name="specialNeed"
          options={specialNeedsOptions.map((item) => ({
            id: item.formValueId,
            label: item.label.en,
          }))}
          selectedValues={formik.values.specialNeed}
          onChange={(val) => formik.setFieldValue("specialNeed", val)}
        />
        {formik.touched.specialNeed && formik.errors.specialNeed && (
          <div style={{ color: "red" }}>{formik.errors.specialNeed}</div>
        )}
      </div>

      {/* Responsibilities & Skills */}
      <div
        style={{
          padding: "1.5rem",
          border: "1px solid #e5e5e5",
          borderRadius: "8px",
          backgroundColor: "#fff",
        }}
      >
        <h3>Key Responsibilities & Skills *</h3>
        <p>(Check all that apply)</p>

        <h3>Supervision & Safety</h3>
        <CheckboxGroup
          name="supervisionSafety"
          options={supervisionSafety.map((item) => ({
            id: item.formValueId,
            label: item.label.en,
          }))}
          selectedValues={formik.values.supervisionSafety}
          onChange={(val) => formik.setFieldValue("supervisionSafety", val)}
        />
        {formik.touched.supervisionSafety &&
          formik.errors.supervisionSafety && (
            <div style={{ color: "red" }}>
              {formik.errors.supervisionSafety}
            </div>
          )}

        <h3 style={{ marginTop: "2rem" }}>Assistance & Care</h3>
        <CheckboxGroup
          name="assistanceCare"
          options={assistanceCare.map((item) => ({
            id: item.formValueId,
            label: item.label.en,
          }))}
          selectedValues={formik.values.assistanceCare}
          onChange={(val) => formik.setFieldValue("assistanceCare", val)}
        />
        {formik.touched.assistanceCare && formik.errors.assistanceCare && (
          <div style={{ color: "red" }}>{formik.errors.assistanceCare}</div>
        )}

        <h3 style={{ marginTop: "2rem" }}>Other</h3>
        <CheckboxGroup
          name="otherOptions"
          options={otherOptions.map((item) => ({
            id: item.formValueId,
            label: item.label.en,
          }))}
          selectedValues={formik.values.otherOptions}
          onChange={(val) => formik.setFieldValue("otherOptions", val)}
        />
        {formik.touched.otherOptions && formik.errors.otherOptions && (
          <div style={{ color: "red" }}>{formik.errors.otherOptions}</div>
        )}
      </div>

      {/* Physical Ability */}
      <div
        style={{
          padding: "1.5rem",
          border: "1px solid #e5e5e5",
          borderRadius: "8px",
          backgroundColor: "#fff",
        }}
      >
        <h3>Physical Ability *</h3>
        <p>(Select highest applicable level)</p>

        <Dropdown
          options={physicalAbility.map(item => ({ value: item.formValueId, label: item.label.en }))}
          value={formik.values.physicalAbility}
          placeholder="Select"
          onChange={(value) => formik.setFieldValue("physicalAbility", value)}
          error={formik.touched.physicalAbility && formik.errors.physicalAbility ? formik.errors.physicalAbility : undefined}
          name="physicalAbility"
        />
        {formik.touched.physicalAbility && formik.errors.physicalAbility && (
          <div style={{ color: "red" }}>{formik.errors.physicalAbility}</div>
        )}
      </div>
 <div style={{ marginTop: "2rem" }}>
        <button type="button" onClick={()=>handleSubmit(formik.values, false)}>Submit Exist</button>
      </div>
      <div style={{ marginTop: "2rem" }}>
        <button type="submit">Submit Details</button>
      </div>
    </form>
  );
};

export default AideExperience;
