@import '../../../styles/global.scss';

.dropdownWrapper {
  position: relative;
  width: 100%;
}

.dropdown {
  position: relative;

  &.hasError {
    .dropdownToggle {
      border-color: #F91313;
    }
  }

  .dropdownToggle {
    background-color: #FFFFFF;
    border: 1px solid #707070;
    border-radius: 4px;
    width: 100%;
    height: 44px;
    color: #515B6F;
    font-weight: 400;
    font-size: 14px;
    line-height: 24px;
    text-align: left;
    padding: 10px 8px;
    outline: none !important;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: flex-start;
    background-image: url(/images/icons/icon-down-arrow.svg);
    background-repeat: no-repeat;
    background-position: right 12px center;

    &:disabled {
      background-color: #F7F7F7;
      font-style: italic;
    }

    &::placeholder {
      color: #9D9D9D;
      font-weight: 400;
      opacity: 1;
    }
  }

  .dropdownMenu {
    background-color: #FFFFFF;
    border-radius: 4px;
    -webkit-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
    -ms-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
    -o-box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
    box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.1);
    max-height: 180px;
    overflow-x: hidden;
    overflow-y: auto;
    position: absolute;
    width: 100%;
    left: 0px;
    top: calc(100% + 8px);
    z-index: 1;
  }

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #F5F5F5;
  }

  &::-webkit-scrollbar-thumb {
    background: #929292;
    border-radius: 24px;
  }

}

.dropdownItem {
  background-color: #FFFFFF;
  border: none;
  display: inline-flex;
  align-items: center;
  justify-content: flex-start;
  font-weight: 500;
  font-size: 15px;
  line-height: 22px;
  height: 44px;
  padding: 4px 12px;
  width: 100%;
}

.error {
  color: #F91313;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  margin-top: 4px;
}
