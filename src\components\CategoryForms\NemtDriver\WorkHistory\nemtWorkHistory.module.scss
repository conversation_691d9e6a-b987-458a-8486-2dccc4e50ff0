.workHistory {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }

  .note {
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    padding: 1rem;
    margin-bottom: 2rem;
    border-radius: 4px;

    h6 {
      font-weight: 600;
      margin-bottom: 0.5rem;
      color: #333;
    }

    p {
      margin: 0;
      color: #666;
      line-height: 1.5;
    }
  }

  .required {
    font-size: 14px;
    color: #666;
    margin-bottom: 2rem;

    sup {
      color: #dc3545;
    }
  }

  .workHistorySection {
    margin-bottom: 2rem;

    > label {
      display: block;
      font-weight: 600;
      margin-bottom: 1rem;
      color: #333;
    }
  }

  .addPeriodBtn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    margin-bottom: 2rem;
    transition: background-color 0.2s;

    &:hover {
      background-color: #0056b3;
    }
  }

  .periodBlock {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    background-color: #fff;

    h4 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #333;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }
  }

  .formRow {
    margin-bottom: 1.5rem;

    label {
      display: block;
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: #333;
    }

    .info {
      font-size: 12px;
      color: #666;
      font-weight: normal;
      font-style: italic;
    }
  }

  .input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.2s;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    &::placeholder {
      color: #999;
    }
  }

  .select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    background-color: white;
    cursor: pointer;
    transition: border-color 0.2s;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
  }

  .textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    min-height: 100px;
    resize: vertical;
    font-family: inherit;
    transition: border-color 0.2s;

    &:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    &::placeholder {
      color: #999;
    }
  }

  .dateFields {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 1rem;
    align-items: end;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .dateField {
    label {
      display: block;
      font-weight: 500;
      margin-bottom: 0.5rem;
      color: #333;
    }
  }

  .checkboxField {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding-top: 1.5rem;

    input[type="checkbox"] {
      width: auto;
      margin: 0;
    }

    label {
      margin: 0;
      font-weight: normal;
      cursor: pointer;
    }
  }

  .error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 0.25rem;
  }

  .removeBtn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 1rem;
    transition: background-color 0.2s;

    &:hover {
      background-color: #c82333;
    }
  }

  .navigationButtons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #ddd;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }
  }

  .backBtn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;

    &:hover {
      background-color: #545b62;
    }
  }

  .rightButtons {
    display: flex;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }

  .saveExitBtn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;

    &:hover {
      background-color: #218838;
    }

    &:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
  }

  .continueBtn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;

    &:hover {
      background-color: #0056b3;
    }

    &:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 18px;
    color: #666;
  }
}
