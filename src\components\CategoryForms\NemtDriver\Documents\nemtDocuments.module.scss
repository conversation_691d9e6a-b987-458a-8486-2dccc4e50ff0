.documents {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
  }

  > p {
    font-size: 16px;
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.5;
  }

  .fileInfo {
    font-style: italic;
    color: #888;
    margin-bottom: 2rem;
  }

  .section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;

    h4 {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 1.5rem;
      color: #333;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }
  }

  .formRow {
    margin-bottom: 2rem;

    label {
      display: block;
      font-weight: 500;
      margin-bottom: 0.75rem;
      color: #333;
      line-height: 1.4;

      sup {
        color: #dc3545;
        font-weight: bold;
      }
    }

    .requiredNote {
      font-size: 14px;
      color: #666;
      font-weight: normal;
      font-style: italic;
      margin-left: 0.5rem;
    }

    .tooltip {
      font-size: 12px;
      color: #666;
      font-weight: normal;
      font-style: italic;
      margin-left: 0.5rem;
      display: inline-block;
    }
  }

  .error {
    color: #dc3545;
    font-size: 14px;
    margin-top: 0.5rem;
    font-weight: 500;
  }

  .navigationButtons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #ddd;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
    }
  }

  .backBtn {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
    font-size: 16px;

    &:hover {
      background-color: #545b62;
    }

    &:disabled {
      background-color: #adb5bd;
      cursor: not-allowed;
    }
  }

  .rightButtons {
    display: flex;
    gap: 1rem;

    @media (max-width: 768px) {
      flex-direction: column;
      width: 100%;
    }
  }

  .saveExitBtn {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
    font-size: 16px;

    &:hover {
      background-color: #218838;
    }

    &:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
  }

  .continueBtn {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s;
    font-size: 16px;

    &:hover {
      background-color: #0056b3;
    }

    &:disabled {
      background-color: #6c757d;
      cursor: not-allowed;
    }
  }

  .loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 18px;
    color: #666;
  }

  // Conditional sections styling
  .conditionalSection {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;

    .conditionalNote {
      font-size: 14px;
      color: #856404;
      font-style: italic;
      margin-bottom: 0.5rem;
    }
  }

  // File upload specific styling
  .fileUploadContainer {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: border-color 0.2s;
    background-color: #fafafa;

    &:hover {
      border-color: #007bff;
      background-color: #f0f8ff;
    }

    &.dragOver {
      border-color: #007bff;
      background-color: #e3f2fd;
    }

    &.hasFile {
      border-color: #28a745;
      background-color: #f8fff9;
    }

    &.error {
      border-color: #dc3545;
      background-color: #fff5f5;
    }
  }

  .uploadButton {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-bottom: 0.5rem;

    &:hover {
      background-color: #0056b3;
    }
  }

  .fileInfo {
    font-size: 12px;
    color: #666;
    margin-top: 0.5rem;
  }

  .fileName {
    font-weight: 500;
    color: #333;
    margin-top: 0.5rem;
  }

  .removeFile {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    margin-left: 0.5rem;

    &:hover {
      background-color: #c82333;
    }
  }

  // Progress bar styling
  .progressBar {
    width: 100%;
    height: 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;

    .progress {
      height: 100%;
      background-color: #007bff;
      transition: width 0.3s ease;
    }
  }

  // Success/error states
  .uploadSuccess {
    color: #28a745;
    font-size: 14px;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;

    &::before {
      content: "✓";
      font-weight: bold;
    }
  }

  .uploadError {
    color: #dc3545;
    font-size: 14px;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;

    &::before {
      content: "✗";
      font-weight: bold;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;

    .section {
      padding: 1rem;
    }

    h3 {
      font-size: 20px;
    }

    .section h4 {
      font-size: 18px;
    }
  }
}
