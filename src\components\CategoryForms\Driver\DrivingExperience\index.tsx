"use client";
import React, { useEffect, useState } from "react";
import RadioGroup from "@/components/Common/RadioGroup";
import CheckboxGroup from "@/components/Common/CheckboxGroup";
import { DriverCategories } from "@/constants";
import { useFormik } from "formik";
import * as Yup from "yup";
import {
  submitDriverDetails,
  fetchDriverDetails,
  FormValue,
} from "@/services/driverFormService";
import { useDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import { useRouter } from "next/navigation";
import css from './drivingExperience.module.scss'
import { toast } from "react-toastify";

interface Option {
  label: string;
  id: number;
}

interface formValues {
  experience: string;
  miles: string;
  trailers: number[];
  straightTrucks: number[];
  buses: number[];
  otherEquipmentText: string;
  tankers: number[];
  transmission: number[];
  routes: number[];
  operatingArea: number[];
  otherEquipment: number[];
}

const DrivingExperience = () => {
  const { updateStepFromApiResponse } = useDriverCategory();
  const router = useRouter();
  const [milesOptions, setMilesOptions] = useState<Option[]>([]);
  const [trailerOptions, setTrailerOptions] = useState<Option[]>([]);
  const [straightTruckOptions, setStraightTruckOptions] = useState<Option[]>(
    []
  );
  const [busOptions, setBusOptions] = useState<Option[]>([]);
  const [otherEquipmentOptions, setOtherEquipmentOptions] = useState<Option[]>(
    []
  );
  const [tankerOptions, setTankerOptions] = useState<Option[]>([]);
  const [transmissionOptions, setTransmissionOptions] = useState<Option[]>([]);
  const [routeTypesOptions, setRouteTypesOptions] = useState<Option[]>([]);
  const [operatingAreaOptions, setOperatingAreaOptions] = useState<Option[]>(
    []
  );

  const experienceOptions = Array.from({ length: 10 }, (_, i) =>
    i === 9 ? "10+ years" : `${i + 1} year${i > 0 ? "s" : ""}`
  );

  const populateFormWithExistingData = async () => {
    try {
      const response = await fetchDriverDetails();
      if (response?.status && response?.data?.driver) {
        const driver = response.data.driver;

        const mapExperienceToLabel = (experience: number): string => {
          if (experience >= 10) return "10+ years";
          if (experience === 1) return "1 year";
          return `${experience} years`;
        };

        const findMilesLabel = (milesId: number): string => {
          const option = milesOptions.find((opt) => opt.id === milesId);
          return option?.label || "";
        };

        const convertOtherEquipment = (
          equipment: (string | number)[]
        ): { numbers: number[]; text: string } => {
          const numbers: number[] = [];
          let text = "";

          equipment.forEach((item) => {
            const num = parseInt(item.toString());
            if (!isNaN(num)) {
              numbers.push(num);
            } else if (typeof item === "string" && item.trim()) {
              text = item;
            }
          });

          return { numbers, text };
        };

        const otherEquipment = convertOtherEquipment(
          driver.otherCdlEquipment || []
        );

        formik.setValues({
          experience: driver.totalVerifiableCdlExperience
            ? mapExperienceToLabel(driver.totalVerifiableCdlExperience)
            : "",
          miles: driver.totalVerifiableMiles
            ? findMilesLabel(driver.totalVerifiableMiles)
            : "",
          trailers: driver.trailerTypes || [],
          straightTrucks: driver.straightTruckTypes || [],
          buses: driver.busTypes || [],
          otherEquipmentText: otherEquipment.text,
          tankers: driver.tankerSpecifics || [],
          transmission:
            driver.transmissionTypes
              ?.map((t) => parseInt(t))
              .filter((t: number) => !isNaN(t)) || [],
          routes: driver.routeTypes || [],
          operatingArea: driver.operatingArea || [],
          otherEquipment: otherEquipment.numbers,
        });
      }
    } catch (error) {
      console.error("Error fetching driver details:", error);
    }
  };

  useEffect(() => {
    const setterMap: Record<
      string,
      React.Dispatch<React.SetStateAction<Option[]>>
    > = {
      setMilesOptions,
      setTrailerOptions,
      setStraightTruckOptions,
      setBusOptions,
      setOtherEquipmentOptions,
      setTankerOptions,
      setTransmissionOptions,
      setRouteTypesOptions,
      setOperatingAreaOptions,
    };

    Promise.all(
      DriverCategories.map(({ url }) => fetch(url).then((res) => res.json()))
    )
      .then((results) => {
        results.forEach((json, index) => {
          const options: Option[] =
            json?.data?.formValues?.map((item: FormValue) => ({
              label: item.label.en,
              id: item.formValueId,
            })) || [];

          const setterKey = DriverCategories[index].setterKey;
          const setterFn = setterMap[setterKey];
          if (setterFn) {
            setterFn(options);
          }
        });
      })
      .catch((err) => {
        console.error("Error loading form options:", err);
      });
  }, []);

  useEffect(() => {
    if (milesOptions.length > 0) {
      populateFormWithExistingData();
    }
  }, [milesOptions]);

  const handleSubmit = async (
    values: formValues,
    shouldContinue: boolean = true
  ) => {
    const milesId =
      milesOptions.find((opt) => opt.label === values.miles)?.id || 0;

    const mapExperience = (value: string): number => {
      if (value === "10+ years") return 10;
      const match = value.match(/^(\d+)/);
      return match ? parseInt(match[1]) : 0;
    };
    const experienceNumber = mapExperience(values.experience);

    const payload = {
      currentStage: 3,
      currentStep: 1,
      driver: {
        totalVerifiableCdlExperience: experienceNumber,
        totalVerifiableMiles: milesId,
        trailerTypes: values.trailers,
        straightTruckTypes: values.straightTrucks,
        busTypes: values.buses,
        otherCdlEquipment: [
          ...values.otherEquipment,
          values.otherEquipmentText,
        ].filter(Boolean),
        tankerSpecifics: values.tankers,
        transmissionTypes: values.transmission,
        routeTypes: values.routes,
        operatingArea: values.operatingArea,
      },
    };

    const response = await submitDriverDetails(payload);
    if (response && response.status) {
      if (shouldContinue) {
        updateStepFromApiResponse(response);
        toast.success("Saved successfully");
        window.scrollTo({ top: 0, behavior: "smooth" });
      } else {
        toast.success("Draft saved successfully! Redirecting to home...");
        setTimeout(() => {
          router.push("/");
        }, 1500);
      }
    } else {
      const errorMessage = response?.message || response?.error?.message || "Failed to save driving experience. Please try again.";
      toast.error(errorMessage);
    }
  };

  const formik = useFormik<formValues>({
    initialValues: {
      experience: "",
      miles: "",
      trailers: [],
      straightTrucks: [],
      buses: [],
      otherEquipmentText: "",
      tankers: [],
      transmission: [],
      routes: [],
      operatingArea: [],
      otherEquipment: [],
    },
    validationSchema: Yup.object({
      tankers: Yup.array()
        .min(1, "Select at least one tanker option")
        .required(),
    }),

    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  return (
    <>
      <h2>CDL -A / CDL - B Experience Level</h2>
      <form onSubmit={formik.handleSubmit} className={css.drivingExperienceForm}>
        <div className={css.formGroup}>
          <label htmlFor="">Total Years of Verifiable CDL Experience (A or B)</label>
          <RadioGroup
            name="experience"
            options={experienceOptions}
            selectedValue={formik.values.experience}
            onChange={(val) => formik.setFieldValue("experience", val)}
          />
        </div>

        <div className={css.formGroup}>
          <label>Approximate Total Verifiable Miles Driven - Commercial CDL</label>
          {milesOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <RadioGroup
              name="miles"
              options={milesOptions.map((opt) => opt.label)}
              selectedValue={formik.values.miles}
              onChange={(val) => formik.setFieldValue("miles", val)}
            />
          )}
        </div>

        <div className={css.formGroup}>
          <label>Type(s) of Trailers/Equipment Hauled/Operated <span>- Check all that apply</span></label>
          {trailerOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <CheckboxGroup
              name="trailers"
              options={trailerOptions}
              selectedValues={formik.values.trailers}
              onChange={(val) => formik.setFieldValue("trailers", val)}
            />
          )}
        </div>

        <div className={css.formGroup}>
          <label>Straight Trucks (Requiring CDL)</label>
          {straightTruckOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <CheckboxGroup
              name="straightTrucks"
              options={straightTruckOptions}
              selectedValues={formik.values.straightTrucks}
              onChange={(val) => formik.setFieldValue("straightTrucks", val)}
            />
          )}
        </div>

        <div className={css.formGroup}>
          <label>Buses (If applicable with CDL & Endorsements)</label>
          {busOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <CheckboxGroup
              name="buses"
              options={busOptions}
              selectedValues={formik.values.buses}
              onChange={(val) => formik.setFieldValue("buses", val)}
            />
          )}
        </div>

        <div className={css.formGroup}>
          <label>Other CDL Required Equipment</label>
          {otherEquipmentOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <CheckboxGroup
              name="otherEquipment"
              options={otherEquipmentOptions}
              selectedValues={formik.values.otherEquipment}
              onChange={(values, otherText) => {
                formik.setFieldValue("otherEquipment", values);
                formik.setFieldValue("otherEquipmentText", otherText || "");
              }}
              otherLabel="Other Equipment"
              otherInputLabel="Specify other CDL equipment"
            />
          )}
        </div>

        <div className={css.formGroup}>
          <label>Tanker Specifics Hauled *</label>
          {tankerOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <CheckboxGroup
              name="tankers"
              options={tankerOptions}
              selectedValues={formik.values.tankers}
              onChange={(val) => formik.setFieldValue("tankers", val)}
            />
          )}
          {formik.touched.tankers && formik.errors.tankers && (
            <div className={css.error}>
              {formik.errors.tankers}
            </div>
          )}
        </div>

        <div className={css.formGroup}>
          <label>Transmission Type Experience - Check all that apply</label>
          {transmissionOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <CheckboxGroup
              name="transmission"
              options={transmissionOptions}
              selectedValues={formik.values.transmission}
              onChange={(val) => formik.setFieldValue("transmission", val)}
            />
          )}
        </div>

        <div className={css.formGroup}>
          <label>Route Types Driven - Check all that apply</label>
          {routeTypesOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <CheckboxGroup
              name="routes"
              options={routeTypesOptions}
              selectedValues={formik.values.routes}
              onChange={(val) => formik.setFieldValue("routes", val)}
            />
          )}
        </div>

        <div className={css.formGroup}>
          <label>Operating Area / Condition Experience - Check all that apply</label>
          {operatingAreaOptions.length === 0 ? (
            <p>Loading options...</p>
          ) : (
            <CheckboxGroup
              name="operatingArea"
              options={operatingAreaOptions}
              selectedValues={formik.values.operatingArea}
              onChange={(val) => formik.setFieldValue("operatingArea", val)}
              customClass={'operatingArea'}
            />
          )}
        </div>
        <div className={css.btnGroup}>
          <button type="button" className={css.back}>
            <img src="/images/icons/arrow_back.svg"/>
            Back
          </button>
          <button
            type="button"
            onClick={() => handleSubmit(formik.values, false)}
            className={css.exit}
          >
            Save Draft and Exit
          </button>
          <button type="submit" className={css.continue}>Save and Continue</button>
        </div>
      </form>
    </>
  );
};

export default DrivingExperience;
