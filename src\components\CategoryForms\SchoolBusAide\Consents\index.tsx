"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import Link from "next/link";
import { toast } from "react-toastify";

import {
  fetchSchoolBusAideFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
} from "@/services/driverFormService";
import DateInput from "@/components/Common/DateInput/DateInput";
import Dropdown from "@/components/Common/Dropdown";
import { useSchoolBusAideCategory } from "@/contexts/CommonDriverCategoryContext";
import { useRouter } from "next/navigation";

interface ConsentsReviewFormValues {
  consentShareProfile: boolean;
  consentBackgroundCheck: boolean;
  consentClearinghouse: boolean;
  consentCertifyInfo: boolean;
  consentAcceptTerms: boolean;
  availability: string;
  availabilitySpecificDate: Date | null;
  employmentType: string[];
  preferredSchedule: string[];
  workSplitShift: string;
}

const ConsentReviews: React.FC = () => {
  const {
    updateStepFromApiResponse,
    goToPreviousStep,
    canGoBack,
    setCurrentStep,
  } = useSchoolBusAideCategory();
  const router = useRouter();
  const [availabilityOptions, setAvailabilityOptions] = useState<FormValue[]>(
    []
  );
  const [employmentTypeOptions, setEmploymentTypeOptions] = useState<
    FormValue[]
  >([]);
  const [scheduleOptions, setSheduleOptions] = useState<FormValue[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const initialValues: ConsentsReviewFormValues = {
    consentShareProfile: false,
    consentBackgroundCheck: false,
    consentClearinghouse: false,
    consentCertifyInfo: false,
    consentAcceptTerms: false,
    availability: "",
    availabilitySpecificDate: null,
    employmentType: [],
    preferredSchedule: [],
    workSplitShift: "",
  };

  const formik = useFormik<ConsentsReviewFormValues>({
    initialValues,
    validationSchema: Yup.object().shape({
      consentShareProfile: Yup.boolean().oneOf(
        [true],
        "Profile sharing consent is required"
      ),
      consentBackgroundCheck: Yup.boolean().oneOf(
        [true],
        "Background check consent is required"
      ),
      consentClearinghouse: Yup.boolean().oneOf(
        [true],
        "FMCSA Clearinghouse consent is required"
      ),
      consentCertifyInfo: Yup.boolean().oneOf(
        [true],
        "Certification of truthfulness is required"
      ),
      consentAcceptTerms: Yup.boolean().oneOf(
        [true],
        "Terms & Privacy agreement is required"
      ),
      availability: Yup.string().required(
        "Date available for work is required"
      ),
      availabilitySpecificDate: Yup.date()
        .nullable()
        .when("availability", {
          is: (val: string) => {
            const option = availabilityOptions.find(
              (opt) => opt.formValueId.toString() === val
            );
            const isSpecificDate = option?.label.en
              .toLowerCase()
              .includes("specific date");
            return isSpecificDate;
          },
          then: (schema) => schema.required("Specific date is required"),
          otherwise: (schema) => schema.notRequired(),
        }),
      employmentType: Yup.array().min(
        1,
        "At least one employment type is required"
      ),
      workSplitShift: Yup.string().required(
        "Split shift preference is required"
      ),
    }),
    onSubmit: async (values) => {
      handleSubmit(values, true);
      // const payload = {
      //   currentStage: 3,
      //   currentStep: 5,
      //   driver: {
      //     consentShareProfile: values.consentShareProfile,
      //     consentBackgroundCheck: values.consentBackgroundCheck,
      //     consentClearinghouse: values.consentClearinghouse,
      //     consentCertifyInfo: values.consentCertifyInfo,
      //     consentAcceptTerms: values.consentAcceptTerms,
      //     preferredSchedule:values.preferredSchedule.map(id => parseInt(id)),
      //     availability: values.availability ? parseInt(values.availability) : undefined,
      //     availabilitySpecificDate: values.availabilitySpecificDate ? values.availabilitySpecificDate.toISOString() : null,
      //     employmentType: values.employmentType.map(id => parseInt(id)),
      //     workSplitShift:
      //       values.workSplitShift === "1" ? true :
      //       values.workSplitShift === "0" ? false : null,
      //   },
      // };
      //   const success = await submitDriverDetails(payload);
      //   if (success) {
      //     toast.success("Submitted successfully");
      //   }
    },
  });

  const handleSubmit = async (
    values: ConsentsReviewFormValues,
    shouldContinue: boolean = true
  ) => {
    console.log(
      "Consents & Review form submission started with values:",
      values
    );

    setIsLoading(true);
    try {
      const payload = {
        currentStage: 3,
        currentStep: 5,
        driver: {
          consentShareProfile: values.consentShareProfile,
          consentBackgroundCheck: values.consentBackgroundCheck,
          consentClearinghouse: values.consentClearinghouse,
          consentCertifyInfo: values.consentCertifyInfo,
          consentAcceptTerms: values.consentAcceptTerms,
          preferredSchedule: values.preferredSchedule.map((id) => parseInt(id)),
          availability: values.availability
            ? parseInt(values.availability)
            : undefined,
          availabilitySpecificDate: values.availabilitySpecificDate
            ? values.availabilitySpecificDate.toISOString()
            : null,
          employmentType: values.employmentType.map((id) => parseInt(id)),
          workSplitShift:
            values.workSplitShift === "1"
              ? true
              : values.workSplitShift === "0"
              ? false
              : null,
        },
      };

      console.log(
        "Submitting consents & review payload:",
        JSON.stringify(payload, null, 2)
      );
      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Profile completed successfully!");
          // Redirect to home or success page
          setTimeout(() => {
            router.push("/");
          }, 2000);
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage =
          response?.message ||
          response?.error?.message ||
          "Failed to complete profile. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error: unknown) {
      console.error("Error submitting consents & review:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to complete profile. Please try again.";
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    const loadData = async () => {
      try {
        const [formFieldData, driverResponse] = await Promise.all([
          fetchSchoolBusAideFormFields(),
          fetchDriverDetails(),
        ]);

        setAvailabilityOptions(
          formFieldData["when-are-you-available-to-start-driver-cdl"] || []
        );
        setEmploymentTypeOptions(
          formFieldData["preferred-employment-types-driver-cdl"] || []
        );
        setSheduleOptions(
          formFieldData["preferred-schedule-driver-bus-aide-assistant"] || []
        );

        const driver = driverResponse?.data?.driver;
        if (driver) {
          const cleaned: ConsentsReviewFormValues = {
            consentShareProfile: Boolean(driver.consentShareProfile),
            consentBackgroundCheck: Boolean(driver.consentBackgroundCheck),
            consentClearinghouse: Boolean(driver.consentClearinghouse),
            consentCertifyInfo: Boolean(driver.consentCertifyInfo),
            consentAcceptTerms: Boolean(driver.consentAcceptTerms),
            availability: driver.availability?.toString() || "",
            availabilitySpecificDate: driver.availabilitySpecificDate
              ? new Date(driver.availabilitySpecificDate)
              : null,
            employmentType:
              driver.employmentType?.map((id: number) => id.toString()) || [],
            preferredSchedule:
              driver.preferredSchedule?.map((id: number) => id.toString()) ||
              [],
            workSplitShift:
              driver.workSplitShift === true
                ? "1"
                : driver.workSplitShift === false
                ? "0"
                : "",
          };
          formik.setValues(cleaned);
        }
      } catch (error) {
        console.error("Error loading Aide data:", error);
        toast.error("Failed to load existing data.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  //   if (isLoading) {
  //     return (
  //       <div style={{ padding: "2rem", textAlign: "center" }}>
  //         <div style={{ fontSize: "18px", color: "#666" }}>
  //           Loading final review...
  //         </div>
  //       </div>
  //     );
  //   }

  return (
    <div style={{ padding: "2rem", maxWidth: "1200px", margin: "0 auto" }}>
      <div style={{ marginBottom: "2rem" }}>
        <h2
          style={{
            fontSize: "24px",
            fontWeight: "600",
            marginBottom: "1rem",
            color: "#333",
          }}
        >
          Step 5: Consents, Availability & Final Review (School Bus Driver)
        </h2>
        <p style={{ fontSize: "16px", color: "#666", marginBottom: "1rem" }}>
          Final step! Review your School Bus Driver profile, agree to consents,
          and specify your availability.
        </p>
      </div>

      <form onSubmit={formik.handleSubmit}>
        {/* Required Consents & Agreements */}
        <div style={{ marginBottom: "2rem" }}>
          <h3
            style={{
              fontSize: "18px",
              fontWeight: "500",
              marginBottom: "1rem",
              color: "#333",
            }}
          >
            Required Consents & Agreements
          </h3>
          <p
            style={{
              fontSize: "14px",
              color: "#666",
              marginBottom: "1.5rem",
              fontStyle: "italic",
            }}
          >
            (All checkboxes below are required to complete your profile *)
          </p>

          {/* Profile Sharing Consent */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "flex",
                alignItems: "flex-start",
                cursor: "pointer",
              }}
            >
              <input
                type="checkbox"
                name="consentShareProfile"
                checked={formik.values.consentShareProfile}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{
                  marginRight: "0.75rem",
                  marginTop: "0.25rem",
                  minWidth: "16px",
                }}
              />
              <div>
                <strong>Profile Sharing Consent: *</strong> I consent to allow
                DriverJobz to share my profile information (excluding full
                sensitive numbers like SSN until Hiring Packet approval) and
                indicate document upload status with registered
                employers/districts on this platform for employment
                consideration. I understand I control full document/detail
                release via Hiring Packet requests
              </div>
            </label>
            {formik.touched.consentShareProfile &&
              formik.errors.consentShareProfile && (
                <div
                  style={{
                    color: "#dc3545",
                    fontSize: "14px",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  {formik.errors.consentShareProfile}
                </div>
              )}
          </div>

          {/* Background Check Consent */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "flex",
                alignItems: "flex-start",
                cursor: "pointer",
              }}
            >
              <input
                type="checkbox"
                name="consentBackgroundCheck"
                checked={formik.values.consentBackgroundCheck}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{
                  marginRight: "0.75rem",
                  marginTop: "0.25rem",
                  minWidth: "16px",
                }}
              />
              <div>
                <strong>Background Check Consent: *</strong> I understand that
                potential employers/districts will conduct background checks as
                a condition of hire, especially for roles involving contact with
                children. This typically includes criminal background checks,
                child abuse registry checks, and verification of employment
                history. I consent to these checks being performed by
                districts/companies I connect with or apply to via DriverJobz.
              </div>
            </label>
            {formik.touched.consentBackgroundCheck &&
              formik.errors.consentBackgroundCheck && (
                <div
                  style={{
                    color: "#dc3545",
                    fontSize: "14px",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  {formik.errors.consentBackgroundCheck}
                </div>
              )}
          </div>

          {/* FMCSA Clearinghouse Consent */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "flex",
                alignItems: "flex-start",
                cursor: "pointer",
              }}
            >
              <input
                type="checkbox"
                name="consentClearinghouse"
                checked={formik.values.consentClearinghouse}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{
                  marginRight: "0.75rem",
                  marginTop: "0.25rem",
                  minWidth: "16px",
                }}
              />
              <div>
                <strong>Certification of Truthfulness: *</strong>I certify that
                all information provided in this application profile is true,
                accurate, and complete to the best of my knowledge. I understand
                that any misrepresentation, falsification, or omission may
                result in disqualification from employment opportunities or
                termination if hired.
              </div>
            </label>
            {formik.touched.consentClearinghouse &&
              formik.errors.consentClearinghouse && (
                <div
                  style={{
                    color: "#dc3545",
                    fontSize: "14px",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  {formik.errors.consentClearinghouse}
                </div>
              )}
          </div>

          {/* Certification of Truthfulness */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "flex",
                alignItems: "flex-start",
                cursor: "pointer",
              }}
            >
              <input
                type="checkbox"
                name="consentCertifyInfo"
                checked={formik.values.consentCertifyInfo}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{
                  marginRight: "0.75rem",
                  marginTop: "0.25rem",
                  minWidth: "16px",
                }}
              />
              <div>
                <strong>Certification of Truthfulness: *</strong> I certify that
                all information provided in this application profile is true,
                accurate, and complete to the best of my knowledge. I understand
                that any misrepresentation, falsification, or omission of
                information may result in disqualification from employment
                opportunities or termination if hired.
              </div>
            </label>
            {formik.touched.consentCertifyInfo &&
              formik.errors.consentCertifyInfo && (
                <div
                  style={{
                    color: "#dc3545",
                    fontSize: "14px",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  {formik.errors.consentCertifyInfo}
                </div>
              )}
          </div>

          {/* Terms & Privacy Agreement */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "flex",
                alignItems: "flex-start",
                cursor: "pointer",
              }}
            >
              <input
                type="checkbox"
                name="consentAcceptTerms"
                checked={formik.values.consentAcceptTerms}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                style={{
                  marginRight: "0.75rem",
                  marginTop: "0.25rem",
                  minWidth: "16px",
                }}
              />
              <div>
                <strong>Terms & Privacy Agreement: *</strong> I acknowledge that
                I have read and agree to the DriverJobz{" "}
                <Link href="/terms-and-conditions" style={{ color: "#007bff" }}>
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link href="/privacy-policy" style={{ color: "#007bff" }}>
                  Privacy Policy
                </Link>
                .
              </div>
            </label>
            {formik.touched.consentAcceptTerms &&
              formik.errors.consentAcceptTerms && (
                <div
                  style={{
                    color: "#dc3545",
                    fontSize: "14px",
                    marginTop: "0.25rem",
                    marginLeft: "1.5rem",
                  }}
                >
                  {formik.errors.consentAcceptTerms}
                </div>
              )}
          </div>
        </div>

        {/* Availability Information */}
        <div style={{ marginBottom: "2rem" }}>
          <h3
            style={{
              fontSize: "18px",
              fontWeight: "500",
              marginBottom: "1rem",
              color: "#333",
            }}
          >
            Availability Information
          </h3>

          {/* Date Available for Work */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "block",
                marginBottom: "0.5rem",
                fontWeight: "500",
                color: "#333",
              }}
            >
              Date Available for Work: *
            </label>
            <Dropdown
              options={availabilityOptions.map(option => ({ value: option.formValueId, label: option.label.en }))}
              value={formik.values.availability}
              placeholder="Select Availability"
              onChange={(value) => formik.setFieldValue("availability", value)}
              error={formik.touched.availability && formik.errors.availability ? formik.errors.availability : undefined}
              name="availability"
            />
            {formik.touched.availability && formik.errors.availability && (
              <div
                style={{
                  color: "#dc3545",
                  fontSize: "14px",
                  marginTop: "0.25rem",
                }}
              >
                {formik.errors.availability}
              </div>
            )}
          </div>

          {/* Specific Date (conditional) */}
          {(() => {
            const selectedOption = availabilityOptions.find(
              (opt) => opt.formValueId.toString() === formik.values.availability
            );
            const showSpecificDate = selectedOption?.label.en
              .toLowerCase()
              .includes("specific date");
            console.log(
              "Availability option:",
              selectedOption?.label.en,
              "Show specific date:",
              showSpecificDate
            );

            return (
              showSpecificDate && (
                <div style={{ marginBottom: "1.5rem" }}>
                  <label
                    style={{
                      display: "block",
                      marginBottom: "0.5rem",
                      fontWeight: "500",
                      color: "#333",
                    }}
                  >
                    Specific Date: *
                  </label>
                  <DateInput
                    selected={formik.values.availabilitySpecificDate}
                    onChange={(date) => {
                      console.log("DateInput onChange called with:", date);
                      if (date !== null) {
                        formik.setFieldValue("availabilitySpecificDate", date);
                      } else {
                        console.log("Date is null, not updating field");
                      }
                    }}
                    placeholder="Select specific date"
                    minDate={new Date()} // Ensure future dates only
                  />

                  {/* Fallback: Regular HTML date input for debugging */}
                  <div
                    style={{
                      marginTop: "0.5rem",
                      fontSize: "12px",
                      color: "#666",
                    }}
                  >
                    <label>Fallback date input (for debugging):</label>
                    <input
                      type="date"
                      value={
                        formik.values.availabilitySpecificDate
                          ? formik.values.availabilitySpecificDate
                              .toISOString()
                              .split("T")[0]
                          : ""
                      }
                      onChange={(e) => {
                        const dateValue = e.target.value
                          ? new Date(e.target.value)
                          : null;
                        console.log("Fallback date input changed:", dateValue);
                        formik.setFieldValue(
                          "availabilitySpecificDate",
                          dateValue
                        );
                      }}
                      style={{
                        width: "100%",
                        padding: "0.5rem",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "14px",
                      }}
                    />
                  </div>
                  {formik.touched.availabilitySpecificDate &&
                    formik.errors.availabilitySpecificDate && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "0.25rem",
                        }}
                      >
                        {formik.errors.availabilitySpecificDate}
                      </div>
                    )}
                </div>
              )
            );
          })()}

          {/* Preferred Employment Type */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "block",
                marginBottom: "0.5rem",
                fontWeight: "500",
                color: "#333",
              }}
            >
              Preferred Employment Type(s): * (Check all that apply)
            </label>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                gap: "0.5rem",
                marginTop: "0.5rem",
              }}
            >
              {employmentTypeOptions.map((option) => (
                <label
                  key={option.formValueId}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    cursor: "pointer",
                  }}
                >
                  <input
                    type="checkbox"
                    name="employmentType"
                    value={option.formValueId}
                    checked={formik.values.employmentType.includes(
                      option.formValueId.toString()
                    )}
                    onChange={(e) => {
                      const value = e.target.value;
                      const currentValues = formik.values.employmentType;
                      if (e.target.checked) {
                        formik.setFieldValue("employmentType", [
                          ...currentValues,
                          value,
                        ]);
                      } else {
                        formik.setFieldValue(
                          "employmentType",
                          currentValues.filter((v) => v !== value)
                        );
                      }
                    }}
                    style={{ marginRight: "0.5rem" }}
                  />
                  {option.label.en}
                </label>
              ))}
            </div>
            {formik.touched.employmentType && formik.errors.employmentType && (
              <div
                style={{
                  color: "#dc3545",
                  fontSize: "14px",
                  marginTop: "0.25rem",
                }}
              >
                {formik.errors.employmentType}
              </div>
            )}
          </div>

          {/* Preferred Route Type */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "block",
                marginBottom: "0.5rem",
                fontWeight: "500",
                color: "#333",
              }}
            >
              Preferred Schedule
            </label>
            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                gap: "0.5rem",
                marginTop: "0.5rem",
              }}
            >
              {scheduleOptions.map((option) => (
                <label
                  key={option.formValueId}
                  style={{
                    display: "flex",
                    alignItems: "center",
                    cursor: "pointer",
                  }}
                >
                  <input
                    type="checkbox"
                    name="preferredSchedule"
                    value={option.formValueId}
                    checked={formik.values.preferredSchedule.includes(
                      option.formValueId.toString()
                    )}
                    onChange={(e) => {
                      const value = e.target.value;
                      const currentValues = formik.values.preferredSchedule;
                      if (e.target.checked) {
                        formik.setFieldValue("preferredSchedule", [
                          ...currentValues,
                          value,
                        ]);
                      } else {
                        formik.setFieldValue(
                          "preferredShedule",
                          currentValues.filter((v) => v !== value)
                        );
                      }
                    }}
                    style={{ marginRight: "0.5rem" }}
                  />
                  {option.label.en}
                </label>
              ))}
            </div>
          </div>

          {/* Willing to Work Split Shift */}
          <div style={{ marginBottom: "1.5rem" }}>
            <label
              style={{
                display: "block",
                marginBottom: "0.5rem",
                fontWeight: "500",
                color: "#333",
              }}
            >
              Willing to Work Split Shift? * (Typical for school bus routes)
            </label>
            <div style={{ display: "flex", gap: "1rem", marginTop: "0.5rem" }}>
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="workSplitShift"
                  value="1"
                  checked={formik.values.workSplitShift === "1"}
                  onChange={formik.handleChange}
                  style={{ marginRight: "0.5rem" }}
                />
                Yes
              </label>
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="workSplitShift"
                  value="0"
                  checked={formik.values.workSplitShift === "0"}
                  onChange={formik.handleChange}
                  style={{ marginRight: "0.5rem" }}
                />
                No
              </label>
              <label
                style={{
                  display: "flex",
                  alignItems: "center",
                  cursor: "pointer",
                }}
              >
                <input
                  type="radio"
                  name="workSplitShift"
                  value="2"
                  checked={formik.values.workSplitShift === "2"}
                  onChange={formik.handleChange}
                  style={{ marginRight: "0.5rem" }}
                />
                Prefer Not
              </label>
            </div>
            {formik.touched.workSplitShift && formik.errors.workSplitShift && (
              <div
                style={{
                  color: "#dc3545",
                  fontSize: "14px",
                  marginTop: "0.25rem",
                }}
              >
                {formik.errors.workSplitShift}
              </div>
            )}
          </div>
        </div>
        {/* review edit */}
        <div style={{ marginTop: "5rem" }}>
          <h2 style={{ marginBottom: "1rem" }}>Final Review</h2>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "1fr 1fr",
              gap: "2rem",
            }}
          >
            {/* Stage 2 - Essentials */}
            <div
              style={{
                border: "1px solid #ccc",
                borderRadius: "12px",
                padding: "1.5rem",
                boxShadow: "0 2px 6px rgba(0,0,0,0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <h3 style={{ margin: 0 }}>Stage 2 - Essentials</h3>
                <button type="button" onClick={() => router.push("/")}>
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button>
              </div>

              <div
                style={{
                  marginTop: "1rem",
                  fontSize: "14px",
                  color: "#444",
                  lineHeight: "1.6",
                }}
              >
                <strong>Category:</strong> [Bus Aide / Bus Assistant]
                <br />
                <strong>License:</strong> [N/A or Standard License Details]
                <br />
                <strong>Safety Summary:</strong> [N/A or Driver Safety Summary]
                <br />
                <strong>ID/ Upload:</strong> [✓]
              </div>
            </div>

            {/* Stage 3 - Experience */}
            <div
              style={{
                border: "1px solid #ccc",
                borderRadius: "12px",
                padding: "1.5rem",
                boxShadow: "0 2px 6px rgba(0,0,0,0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <h3 style={{ margin: 0 }}>Stage 3 - Step 1: Experience </h3>
                <button
                  type="button"
                  onClick={() => {
                    setCurrentStep(1);
                    router.push("/ ");
                  }}
                >
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button>
              </div>

              <div
                style={{
                  marginTop: "1rem",
                  fontSize: "14px",
                  color: "#444",
                  lineHeight: "1.6",
                }}
              >
                <strong> Aide Exp:</strong> 2 Years
                <br />
                <strong>Students:</strong> [Elementary, Middle, Special Needs]
                <br />
                <strong>Skills:</strong> [Supervision, Behavior Mgmt, Wheelchair
                Securement]
                <br />
              </div>
            </div>

            {/* Stage 3 - History */}
            <div
              style={{
                border: "1px solid #ccc",
                borderRadius: "12px",
                padding: "1.5rem",
                boxShadow: "0 2px 6px rgba(0,0,0,0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <h3 style={{ margin: 0 }}>Stage 3 - Step 2: History</h3>
                <button
                  type="button"
                  onClick={() => {
                    setCurrentStep(2);
                    router.push("/");
                  }}
                >
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button>
              </div>

              <div
                style={{
                  marginTop: "1rem",
                  fontSize: "14px",
                  color: "#444",
                  lineHeight: "1.6",
                }}
              >
                <strong>Periods Reported</strong>[2] (Covering [MM/YYYY] -
                Present)
                <br />
                *(Brief summary showing Aide/School employment)*
                <br />
              </div>
            </div>

            {/* Stage 3 - Skills & Certs */}
            <div
              style={{
                border: "1px solid #ccc",
                borderRadius: "12px",
                padding: "1.5rem",
                boxShadow: "0 2px 6px rgba(0,0,0,0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <h3 style={{ margin: 0 }}>Stage 3 - Step 3: Skills & Cert</h3>
                <button
                  type="button"
                  onClick={() => {
                    setCurrentStep(3);
                    router.push("/");
                  }}
                >
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button>
              </div>

              <div
                style={{
                  marginTop: "1rem",
                  fontSize: "14px",
                  color: "#444",
                  lineHeight: "1.6",
                }}
              >
                <strong>CPR:</strong> [Yes, Exp: MM/DD/YYYY] | First Aid: [Yes,
                Exp: MM/DD/YYYY]
                <br />
                <strong> Behavior Training:</strong> [Yes] | State Clearances:
                [Yes, Current]
                <br />
              </div>
            </div>
            {/* documnets */}
            <div
              style={{
                border: "1px solid #ccc",
                borderRadius: "12px",
                padding: "1.5rem",
                boxShadow: "0 2px 6px rgba(0,0,0,0.1)",
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <h3 style={{ margin: 0 }}>Stage 3 - Step 4: Documents</h3>
                <button
                  type="button"
                  onClick={() => {
                    setCurrentStep(3);
                    router.push("/");
                  }}
                >
                  <img src="/images/icons/icon-edit.svg" alt="edit" /> Edit
                </button>
              </div>

              <div
                style={{
                  marginTop: "1rem",
                  fontSize: "14px",
                  color: "#444",
                  lineHeight: "1.6",
                }}
              >
                <strong>CPR Card:</strong> [✓] | First Aid Card: [✓] |
                Clearances: [Uploaded]
                <br />
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: "2rem",
            padding: "1.5rem",
            borderTop: "1px solid #e5e5e5",
          }}
        >
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              &lt; Back  
            </button>
          )}

          <div style={{ display: "flex", gap: "1rem" }}>
            <button
              type="submit"
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
                fontWeight: "600",
              }}
            >
              {isLoading
                ? "Completing..."
                : "Complete Full Profile & Activate Features"}
            </button>

            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              Save & Exit (Complete Later)
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default ConsentReviews;
