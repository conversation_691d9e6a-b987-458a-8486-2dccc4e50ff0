import { getCookie } from "cookies-next";

export const isUserLoggedIn = (): boolean => {
    return !!getCookie("authToken");
};

type FlatFormValues = Record<string, string | number | null | undefined>;

export const cleanFlatFormValues = (values: FlatFormValues): FlatFormValues => {
  return Object.entries(values).reduce((acc, [key, value]) => {
    if (typeof value === 'string') {
      acc[key] = value.trim() === '' ? null : value;
    } else {
      acc[key] = value;
    }
    return acc;
  }, {} as FlatFormValues);
};
