"use client";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import {
  fetchLeavingOptions,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
} from "@/services/driverFormService";
import { useFormik, FormikErrors } from "formik";
import * as Yup from "yup";
import { useEffect, useState } from "react";
import { getStates, State } from "@/services/locationService";
import { toast } from "react-toastify";
import { useDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import { useRouter } from "next/navigation";
import css from './employmentHistory.module.scss';

interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
}

interface EmploymentEntry {
  id: string;
  isUnemployed: boolean;
  employerName: string;
  phone: string;
  managerName: string;
  address: Address;
  position: string;
  fmcsatest: string;
  operatedCmv: string;
  mayContact: string;
  startDate: Date | null;
  endDate: Date | null;
  currentlyWorking: boolean;
  reasonForLeaving: string;
  explanation: string;
}

interface EmploymentHistoryFormValues {
  employmentHistory: EmploymentEntry[];
}
function isEmploymentErrorArray(
  value: unknown
): value is Array<FormikErrors<EmploymentEntry>> {
  return Array.isArray(value);
}

const EmploymentHistory = () => {
  const { updateStepFromApiResponse } = useDriverCategory();
  const router = useRouter();
  const [reasonOptions, setReasonOptions] = useState<FormValue[]>([]);
  const [states, setStates] = useState<State[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadAllData = async () => {
      try {
        const [leavingOptions, stateList, detailsResponse] = await Promise.all([
          fetchLeavingOptions(),
          getStates(),
          fetchDriverDetails(),
        ]);

        setReasonOptions(leavingOptions);
        setStates(stateList);

        const history = detailsResponse?.data?.driver?.driverEmploymentHistory;

        if (history && history.length > 0) {
          const mappedHistory = history.map((emp) => ({
            id: `employment-${emp.driverEmploymentHistoryId || Date.now()
              }-${Math.random()}`,
            isUnemployed: emp.isUnemployment || false,
            employerName: emp.employerName || "",
            phone: emp.employerPhone || "",
            managerName: emp.employerManagerName || "",
            address: {
              street: emp.employerStreet || "",
              city: emp.employerCity || "",
              state: emp.employerState || "",
              zipCode: emp.employerZip || "",
            },
            position: emp.positionHeld || "",
            fmcsatest: emp.subjectToFmcsa ? "yes" : "no",
            operatedCmv: emp.operatedCmv ? "yes" : "no",
            mayContact: emp.contactPermission ? "yes" : "no",
            startDate: emp.startDate ? new Date(emp.startDate) : null,
            endDate: emp.endDate ? new Date(emp.endDate) : null,
            currentlyWorking: emp.isCurrent || false,
            reasonForLeaving: emp.reasonForLeaving || "",
            explanation: emp.explanation || "",
          }));

          formik.setFieldValue("employmentHistory", mappedHistory);
        }
      } catch (err) {
        console.error("Failed to fetch data:", err);
      } finally {
        setIsLoading(false);
      }
    };

    loadAllData();
  }, []);

  // useEffect(() => {
  //   if (reasonOptions.length > 0 && states.length > 0) {
  //     populateFormWithExistingData();
  //   }
  // }, [reasonOptions, states]);

  useEffect(() => {
    setIsLoading(false); // Add this line
  }, []);

  const createEmptyEmploymentEntry = () => ({
    id: `employment-${Date.now()}-${Math.random()}`,
    isUnemployed: false,
    employerName: "",
    phone: "",
    managerName: "",
    address: {
      street: "",
      city: "",
      state: "",
      zipCode: "",
    },
    position: "",
    fmcsatest: "yes",
    operatedCmv: "yes",
    mayContact: "yes",
    startDate: null,
    endDate: null,
    currentlyWorking: false,
    reasonForLeaving: "",
    explanation: "",
  });

  const initialValues: EmploymentHistoryFormValues = {
    employmentHistory: [createEmptyEmploymentEntry()],
  };

  const validationSchema = Yup.object().shape({
    employmentHistory: Yup.array()
      .min(3, "At least 3 employment periods are required")
      .of(
        Yup.object().shape({
          isUnemployed: Yup.boolean(),
          currentlyWorking: Yup.boolean(),
          employerName: Yup.string().when("isUnemployed", {
            is: false,
            then: (schema) => schema.required("Employer name is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
          phone: Yup.string().when("isUnemployed", {
            is: false,
            then: (schema) =>
              schema
                .matches(/^[0-9]{10,15}$/, "Invalid phone number")
                .required("Phone is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
          managerName: Yup.string(),
          address: Yup.object().when("isUnemployed", {
            is: false,
            then: (schema) => schema.shape({
              street: Yup.string().required("Street address is required"),
              city: Yup.string().required("City is required"),
              state: Yup.string().required("State is required"),
              zipCode: Yup.string()
                .matches(/^\d{5}$/, "Enter a valid zip code")
                .notRequired(),
            }),
            otherwise: (schema) => schema.shape({
              street: Yup.string().notRequired(),
              city: Yup.string().notRequired(),
              state: Yup.string().notRequired(),
              zipCode: Yup.string()
                .matches(/^\d{5}$/, "Enter a valid zip code")
                .notRequired(),
            }),
          }),
          position: Yup.string().when("isUnemployed", {
            is: false,
            then: (schema) => schema.required("Position is required"),
            otherwise: (schema) => schema.notRequired(),
          }),
          fmcsatest: Yup.string(),
          operatedCmv: Yup.string(),
          mayContact: Yup.string(),
          startDate: Yup.date().nullable().required("Start Date is required"),
          endDate: Yup.date()
            .nullable()
            .when("currentlyWorking", {
              is: false,
              then: (schema) => schema.required("End Date is required"),
              otherwise: (schema) => schema.notRequired(),
            }),

          // reasonForLeaving: Yup.string().required("Required is required"),
          explanation: Yup.string().max(250),
        })
      ),
  });

  const handleSubmit = async (values: EmploymentHistoryFormValues, shouldContinue: boolean = true) => {
    if (!values.employmentHistory || values.employmentHistory.length < 3) {
      toast.error("At least 3 employment periods are required.");
      return;
    }

    const payload = {
      currentStage: 3,
      currentStep: 2,
      driver: {
        driverEmploymentHistory: values.employmentHistory.map(
          (employment, index) => ({
            isUnemployment: employment.isUnemployed,
            employerName: employment.isUnemployed
              ? undefined
              : employment.employerName,
            employerStreet: employment.isUnemployed
              ? undefined
              : employment.address.street,
            employerCity: employment.isUnemployed
              ? undefined
              : employment.address.city,
            employerState: employment.isUnemployed
              ? undefined
              : employment.address.state,
            employerZip: employment.isUnemployed
              ? undefined
              : employment.address.zipCode,
            employerPhone: employment.isUnemployed
              ? undefined
              : employment.phone,
            employerWebsite: "",
            employerManagerName: employment.isUnemployed
              ? undefined
              : employment.managerName,
            positionHeld: employment.isUnemployed
              ? undefined
              : employment.position,
            subjectToFmcsa: employment.isUnemployed
              ? undefined
              : employment.fmcsatest === "yes",
            operatedCmv: employment.isUnemployed
              ? undefined
              : employment.operatedCmv === "yes",
            contactPermission: employment.isUnemployed
              ? undefined
              : employment.mayContact === "yes",
            startDate: employment.startDate
              ? new Date(employment.startDate).toISOString()
              : null,
            endDate: employment.currentlyWorking
              ? undefined
              : employment.endDate
                ? new Date(employment.endDate).toISOString()
                : null,
            isCurrent: employment.currentlyWorking,
            reasonForLeaving: employment.reasonForLeaving,
            explanation: employment.explanation || "",
            rank: index + 1,
          })
        ),
      },
    };

    const response = await submitDriverDetails(payload);
    if (response && response.status) {
      if (shouldContinue) {
        updateStepFromApiResponse(response);
        toast.success("Saved successfully");
        window.scrollTo({ top: 0, behavior: 'smooth' });
      } else {
        toast.success("Draft saved successfully! Redirecting to home...");
        setTimeout(() => {
          router.push("/");
        }, 1500);
      }
    } else {
      const errorMessage = response?.message || response?.error?.message || "Failed to save employment history. Please try again.";
      toast.error(errorMessage);
    }
  };

  const formik = useFormik<EmploymentHistoryFormValues>({
    initialValues,
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  const addEmployment = () => {
    const newEntry: EmploymentEntry = createEmptyEmploymentEntry();
    formik.setFieldValue("employmentHistory", [
      ...formik.values.employmentHistory,
      newEntry,
    ]);
  };

  const removeEmployment = (index: number) => {
    const updated = [...formik.values.employmentHistory];
    updated.splice(index, 1);
    formik.setFieldValue("employmentHistory", updated);
  };

  if (isLoading) {
    return <div>Loading employment history...</div>;
  }

  return (
    <div className={css.employmentHistory}>
      <div className={css.note}>
        <h6>Important</h6>
        <p>Per DOT regulations (49 CFR §391.21), you must provide a complete history of all employment for the **past 3 years**. Please also list any periods of unemployment during this time. Providing up to 10 years of history is recommended for faster processing by employers. Potential employers are required by law to verify this information.</p>
      </div>
      <label className={css.mt28}>Employment / Unemployment Periods <span>- Start with most recent</span></label>
      <button type="button" onClick={addEmployment} className={css.addPeriodBtn}>
        + Add Employment / Unemployment Period
      </button>
      {formik.touched.employmentHistory &&
        formik.errors.employmentHistory &&
        typeof formik.errors.employmentHistory === "string" && (
          <p style={{ color: "red", fontSize: "12px", marginTop: "0.5rem" }}>
            {formik.errors.employmentHistory}
          </p>
        )}
      <form onSubmit={formik.handleSubmit}>
        <div>
          {formik.values.employmentHistory.map((employment, index) => (
            <div key={index} className={css.employmentHistoryRow}>
              <div style={{ marginTop: "1rem" }}>
                <label>Employment Period - {index + 1}</label>
                <div className={`${css.checkBox} ${css.mb28}`}>
                  <input
                    type="checkbox"
                    name={`employmentHistory[${index}].isUnemployed`}
                    checked={employment.isUnemployed}
                    onChange={formik.handleChange}
                  />
                  <span className={css.checkmark}></span>
                  <p>Check here if this was a period of <strong>Unemployment</strong></p>
                </div>
              </div>

              {!employment.isUnemployed && (
                <>
                  <div className={css.formRow}>
                    <div className={css.col03}>
                      <label>Employer Name</label>
                      <input
                        type="text"
                        name={`employmentHistory[${index}].employerName`}
                        value={employment.employerName}
                        onChange={formik.handleChange}
                        placeholder="Employer Name"
                      />
                      {formik.touched.employmentHistory?.[index]?.employerName &&
                        isEmploymentErrorArray(formik.errors.employmentHistory) &&
                        formik.errors.employmentHistory[index]?.employerName && (
                          <p className={css.error}>{formik.errors.employmentHistory[index]?.employerName}</p>
                        )}
                    </div>

                    <div className={css.col03}>
                      <label>Employer Phone Number</label>
                      <input
                        type="text"
                        name={`employmentHistory[${index}].phone`}
                        value={employment.phone}
                        onChange={formik.handleChange}
                        placeholder="Phone"
                        maxLength={10}
                      />
                      {formik.touched.employmentHistory?.[index]?.phone &&
                        isEmploymentErrorArray(formik.errors.employmentHistory) &&
                        formik.errors.employmentHistory[index]?.phone && (
                          <p className={css.error}>{formik.errors.employmentHistory[index]?.phone}</p>
                        )}
                    </div>

                    <div className={css.col03}>
                      <label>Manager Name</label>
                      <input
                        type="text"
                        name={`employmentHistory[${index}].managerName`}
                        value={employment.managerName}
                        onChange={formik.handleChange}
                        placeholder="Manager Name"
                      />
                    </div>

                    <div className={css.col03}>
                      <label>Employer Street Address</label>
                      <input
                        type="text"
                        name={`employmentHistory[${index}].address.street`}
                        value={employment.address.street}
                        onChange={formik.handleChange}
                        placeholder="Street"
                      />
                      {formik.touched.employmentHistory?.[index]?.address?.street &&
                        isEmploymentErrorArray(formik.errors.employmentHistory) &&
                        formik.errors.employmentHistory[index]?.address?.street && (
                          <p className={css.error}>{formik.errors.employmentHistory[index]?.address?.street}</p>
                        )}
                    </div>

                    <div className={css.col03}>
                      <label>City</label>
                      <input
                        type="text"
                        name={`employmentHistory[${index}].address.city`}
                        value={employment.address.city}
                        onChange={formik.handleChange}
                        placeholder="City"
                      />
                      {formik.touched.employmentHistory?.[index]?.address?.city &&
                        isEmploymentErrorArray(formik.errors.employmentHistory) &&
                        formik.errors.employmentHistory[index]?.address?.city && (
                          <p className={css.error}>{formik.errors.employmentHistory[index]?.address?.city}</p>
                        )}
                    </div>

                    <div className={css.col03}>
                      <label>State</label>
                      <Dropdown
                        options={states.map(item => ({ value: item.name.en, label: item.name.en }))}
                        value={employment.address.state}
                        placeholder="Select State"
                        onChange={(value) => formik.setFieldValue(`employmentHistory[${index}].address.state`, value)}
                        error={formik.touched.employmentHistory?.[index]?.address?.state &&
                          isEmploymentErrorArray(formik.errors.employmentHistory) &&
                          formik.errors.employmentHistory[index]?.address?.state ?
                          formik.errors.employmentHistory[index]?.address?.state : undefined}
                        name={`employmentHistory[${index}].address.state`}
                      />
                    </div>

                    <div className={css.col03}>
                      <label>Zip Code</label>
                      <input
                        type="text"
                        name={`employmentHistory[${index}].address.zipCode`}
                        value={employment.address.zipCode}
                        onChange={formik.handleChange}
                        placeholder="Zip Code"
                        maxLength={5}
                      />
                      {formik.touched.employmentHistory?.[index]?.address?.zipCode &&
                        isEmploymentErrorArray(formik.errors.employmentHistory) &&
                        formik.errors.employmentHistory[index]?.address?.zipCode && (
                          <p className={css.error}>{formik.errors.employmentHistory[index]?.address?.zipCode}</p>
                        )}
                    </div>

                    <div className={css.col03}>
                      <label>Position Held</label>
                      <input
                        type="text"
                        name={`employmentHistory[${index}].position`}
                        value={employment.position}
                        onChange={formik.handleChange}
                        placeholder="Position"
                      />
                      {formik.touched.employmentHistory?.[index]?.position &&
                        isEmploymentErrorArray(formik.errors.employmentHistory) &&
                        formik.errors.employmentHistory[index]?.position && (
                          <p className={css.error}>{formik.errors.employmentHistory[index]?.position}</p>
                        )}
                    </div>
                  </div>

                  <div className={`${css.formRow} ${css.dBlaco}`}>
                    <label>Were you subject to FMCSA Drug & Alcohol Testing Regulations at this job?</label>
                    <RadioGroup
                      name={`employmentHistory[${index}].fmcsatest`}
                      options={["yes", "no", "unsure"]}
                      selectedValue={employment.fmcsatest}
                      onChange={(value) =>
                        formik.setFieldValue(`employmentHistory[${index}].fmcsatest`, value)
                      }
                    />
                  </div>

                  <div className={`${css.formRow} ${css.dBlaco}`}>
                    <label>Did you operate a Commercial Motor Vehicle (CMV) requiring a CDL for this employer?</label>
                    <RadioGroup
                      name={`employmentHistory[${index}].operatedCmv`}
                      options={["yes", "no"]}
                      selectedValue={employment.operatedCmv}
                      onChange={(value) =>
                        formik.setFieldValue(`employmentHistory[${index}].operatedCmv`, value)
                      }
                    />
                  </div>

                  <div className={`${css.formRow} ${css.dBlaco}`}>
                    <label>May we contact this employer for verification?</label>
                    <RadioGroup
                      name={`employmentHistory[${index}].mayContact`}
                      options={["yes", "no"]}
                      selectedValue={employment.mayContact}
                      onChange={(value) =>
                        formik.setFieldValue(`employmentHistory[${index}].mayContact`, value)
                      }
                    />
                  </div>
                </>
              )}

              <div className={css.formRow}>
                <div className={css.col03}>
                  <label>Start Date - From *</label>
                  <DateInput
                    selected={employment.startDate}
                    onChange={(date) =>
                      formik.setFieldValue(`employmentHistory[${index}].startDate`, date)
                    }
                  />
                  {formik.touched.employmentHistory?.[index]?.startDate &&
                    isEmploymentErrorArray(formik.errors.employmentHistory) &&
                    formik.errors.employmentHistory[index]?.startDate && (
                      <p className={css.error}>{formik.errors.employmentHistory[index]?.startDate}</p>
                    )}
                </div>

                <div className={css.col03}>
                  <label>End Date - To *</label>
                  <DateInput
                    selected={employment.endDate}
                    onChange={(date) =>
                      formik.setFieldValue(`employmentHistory[${index}].endDate`, date)
                    }
                  />
                  {formik.touched.employmentHistory?.[index]?.endDate &&
                    isEmploymentErrorArray(formik.errors.employmentHistory) &&
                    formik.errors.employmentHistory[index]?.endDate && (
                      <p className={css.error}>{formik.errors.employmentHistory[index]?.endDate}</p>
                    )}
                </div>

                <div className={`${css.col03} ${css.mt36}`}>
                  <div className={css.checkBox}>
                    <input
                      type="checkbox"
                      name={`employmentHistory[${index}].currentlyWorking`}
                      checked={employment.currentlyWorking}
                      onChange={formik.handleChange}
                    />
                    <span className={css.checkmark}></span>
                    <p>I am currently working in this role</p>
                  </div>
                </div>
              </div>

              <div className={css.formRow}>
                <label>
                  Reason for Leaving <span>- Select "Still Employed" if current, "Unemployment Period" if checked above</span>
                </label>
                {reasonOptions.length === 0 ? (
                  <p>Loading options...</p>
                ) : (
                  <RadioGroup
                    name={`employmentHistory[${index}].reasonForLeaving`}
                    options={reasonOptions.map((opt) => opt.label.en)}
                    selectedValue={employment.reasonForLeaving}
                    onChange={(value) =>
                      formik.setFieldValue(`employmentHistory[${index}].reasonForLeaving`, value)
                    }
                  />
                )}
              </div>

              <div className={`${css.formRow} ${css.dBlaco}`}>
                <label>Brief Explanation</label>
                <textarea
                  name={`employmentHistory[${index}].explanation`}
                  value={employment.explanation}
                  onChange={formik.handleChange}
                  placeholder="Brief explanation"
                  maxLength={250}
                />
                <span className={css.characterLimit}>Max 250 characters</span>
              </div>

              <button type="button" onClick={() => removeEmployment(index)} className={css.removeHistory}>
                <img src="/images/icons/icon-close.svg" alt="" />
                Remove Period
              </button>
            </div>
          ))}

        </div>
        <div className={css.btnGroup}>
          <button type="button" className={css.back}>
            <img src="/images/icons/arrow_back.svg" />
            Back
          </button>
          <button
            type="button"
            onClick={() => handleSubmit(formik.values, false)}
            className={css.exit}
          >
            Save Draft and Exit
          </button>
          <button type="submit" className={css.continue}>Save and Continue</button>
        </div>
      </form>
    </div>
  );
};

export default EmploymentHistory;
