"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  submitDriverDetails,
  fetchDriverDetails,
} from "@/services/driverFormService";
import BrowseFiles, { FileItem } from "@/components/Browse/BrowseFiles";
import css from './nemtDocuments.module.scss';

interface DocumentsFormValues {
  cprCertificationFiles: FileItem[];
  firstAidCertificationFiles: FileItem[];
  patsCertificationFiles: FileItem[];
  mavtCertificationFiles: FileItem[];
  defensiveDrivingCertificationFiles: FileItem[];
  stateNemtCertificationFiles: FileItem[];
  dotMedicalCardFiles: FileItem[];
  medicalVarianceDocumentFiles: FileItem[];
  autoInsuranceFiles: FileItem[];
  vehicleRegistrationFiles: FileItem[];
  socialSecurityCardFiles: FileItem[];
  rightToWorkFiles: FileItem[];
  resumeFiles: FileItem[];
}

interface MedicalData {
  cprCertified: string;
  firstAidCertified: string;
  patsCertified: string;
  mavtCertified: string;
  defensiveDriving: string;
  stateNemtCert: string;
  dotMedicalRequired: string;
  dotMedicalStatus: string | null | undefined;
}

const NemtDocuments: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [medicalData, setMedicalData] = useState<MedicalData | null>(null);

  const validationSchema = Yup.object({
    cprCertificationFiles: Yup.array().when([], {
      is: () => medicalData?.cprCertified === "Yes",
      then: (schema) => schema.min(1, "CPR certification is required"),
      otherwise: (schema) => schema,
    }),
    firstAidCertificationFiles: Yup.array().when([], {
      is: () => medicalData?.firstAidCertified === "Yes",
      then: (schema) => schema.min(1, "First Aid certification is required"),
      otherwise: (schema) => schema,
    }),
    patsCertificationFiles: Yup.array().when([], {
      is: () => medicalData?.patsCertified === "Yes, Current",
      then: (schema) => schema.min(1, "PATS certification is required"),
      otherwise: (schema) => schema,
    }),
    mavtCertificationFiles: Yup.array().when([], {
      is: () => medicalData?.mavtCertified === "Yes, Current",
      then: (schema) => schema.min(1, "MAVT/MAVO certification is required"),
      otherwise: (schema) => schema,
    }),
    stateNemtCertificationFiles: Yup.array().when([], {
      is: () => medicalData?.stateNemtCert === "yes",
      then: (schema) => schema.min(1, "State NEMT certification is required"),
      otherwise: (schema) => schema,
    }),
    dotMedicalCardFiles: Yup.array().when([], {
      is: () => (medicalData?.dotMedicalRequired === "Yes (Driving larger vehicle / Employer policy)" ||
                 medicalData?.dotMedicalRequired === "Unsure") &&
                !!medicalData?.dotMedicalStatus &&
                medicalData.dotMedicalStatus !== "disqualified" &&
                medicalData.dotMedicalStatus !== "n/a",
      then: (schema) => schema.min(1, "DOT Medical Card is required"),
      otherwise: (schema) => schema,
    }),
    medicalVarianceDocumentFiles: Yup.array().when([], {
      is: () => medicalData?.dotMedicalStatus === "variance",
      then: (schema) => schema.min(1, "Medical variance document is required"),
      otherwise: (schema) => schema,
    }),
  });

  const formik = useFormik<DocumentsFormValues>({
    initialValues: {
      cprCertificationFiles: [],
      firstAidCertificationFiles: [],
      patsCertificationFiles: [],
      mavtCertificationFiles: [],
      defensiveDrivingCertificationFiles: [],
      stateNemtCertificationFiles: [],
      dotMedicalCardFiles: [],
      medicalVarianceDocumentFiles: [],
      autoInsuranceFiles: [],
      vehicleRegistrationFiles: [],
      socialSecurityCardFiles: [],
      rightToWorkFiles: [],
      resumeFiles: [],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load existing driver data to get medical information for validation
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver;
          const medical = (driver as any).nemtMedical;
          
          if (medical) {
            setMedicalData({
              cprCertified: medical.cprCertified || "",
              firstAidCertified: medical.firstAidCertified || "",
              patsCertified: medical.patsCertified || "",
              mavtCertified: medical.mavtCertified || "",
              defensiveDriving: medical.defensiveDriving || "",
              stateNemtCert: medical.stateNemtCert || "",
              dotMedicalRequired: medical.dotMedicalRequired || "",
              dotMedicalStatus: medical.dotMedicalStatus || "",
            });
          }

          // Load existing document data if available
          const documents = (driver as any).nemtDocuments;
          if (documents) {
            // Note: File objects can't be restored from API, but we can show existing file names
            // This would typically be handled by showing existing file names/links
          }
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load data. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (values: DocumentsFormValues, shouldContinue: boolean) => {
    try {
      setIsLoading(true);

      // Prepare document data with file information
      const payload = {
        nemtDocuments: {
          cprCertificationFiles: values.cprCertificationFiles,
          firstAidCertificationFiles: values.firstAidCertificationFiles,
          patsCertificationFiles: values.patsCertificationFiles,
          mavtCertificationFiles: values.mavtCertificationFiles,
          defensiveDrivingCertificationFiles: values.defensiveDrivingCertificationFiles,
          stateNemtCertificationFiles: values.stateNemtCertificationFiles,
          dotMedicalCardFiles: values.dotMedicalCardFiles,
          medicalVarianceDocumentFiles: values.medicalVarianceDocumentFiles,
          autoInsuranceFiles: values.autoInsuranceFiles,
          vehicleRegistrationFiles: values.vehicleRegistrationFiles,
          socialSecurityCardFiles: values.socialSecurityCardFiles,
          rightToWorkFiles: values.rightToWorkFiles,
          resumeFiles: values.resumeFiles,
        },
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Documents uploaded successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to upload documents. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting documents:", error);
      toast.error("Failed to upload documents. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const isRequired = (fieldName: string): boolean => {
    if (!medicalData) return false;

    switch (fieldName) {
      case 'cprCertificationFiles':
        return medicalData.cprCertified === "Yes";
      case 'firstAidCertificationFiles':
        return medicalData.firstAidCertified === "Yes";
      case 'patsCertificationFiles':
        return medicalData.patsCertified === "Yes, Current";
      case 'mavtCertificationFiles':
        return medicalData.mavtCertified === "Yes, Current";
      case 'stateNemtCertificationFiles':
        return medicalData.stateNemtCert === "yes";
      case 'dotMedicalCardFiles':
        return (medicalData.dotMedicalRequired === "Yes (Driving larger vehicle / Employer policy)" ||
                medicalData.dotMedicalRequired === "Unsure") &&
               !!medicalData.dotMedicalStatus &&
               medicalData.dotMedicalStatus !== "disqualified" &&
               medicalData.dotMedicalStatus !== "n/a";
      case 'medicalVarianceDocumentFiles':
        return medicalData.dotMedicalStatus === "variance";
      default:
        return false;
    }
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.documents}>
      <h3>Step 4: Additional Document Uploads (NEMT Driver)</h3>
      <p>Upload required certifications and other relevant documents. Your basic ID/License was uploaded in Stage 2. Documents are shared only via approved "Hiring Packet Requests".</p>
      <p className={css.fileInfo}><em>(Accepted formats: JPG, PNG, PDF. Max size: 5MB per file)</em></p>
      
      <form onSubmit={formik.handleSubmit}>
        {/* Required/Recommended Certifications */}
        <div className={css.section}>
          <h4>Required/Recommended Certifications</h4>
          
          <div className={css.formRow}>
            <BrowseFiles
              label={`Upload CPR Certification Card: ${isRequired('cprCertificationFiles') ? '*' : ''} ${isRequired('cprCertificationFiles') ? '(Required if indicated \'Yes\' in Step 3)' : ''}`}
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('cprCertificationFiles', files)}
              initialFiles={formik.values.cprCertificationFiles}
            />
            {formik.touched.cprCertificationFiles && formik.errors.cprCertificationFiles && (
              <p className={css.error}>
                {typeof formik.errors.cprCertificationFiles === 'string'
                  ? formik.errors.cprCertificationFiles
                  : 'CPR certification is required'}
              </p>
            )}
          </div>

          <div className={css.formRow}>
            <BrowseFiles
              label={`Upload First Aid Certification Card: ${isRequired('firstAidCertificationFiles') ? '*' : ''} ${isRequired('firstAidCertificationFiles') ? '(Required if indicated \'Yes\' in Step 3)' : ''}`}
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('firstAidCertificationFiles', files)}
              initialFiles={formik.values.firstAidCertificationFiles}
            />
            {formik.touched.firstAidCertificationFiles && formik.errors.firstAidCertificationFiles && (
              <p className={css.error}>
                {typeof formik.errors.firstAidCertificationFiles === 'string'
                  ? formik.errors.firstAidCertificationFiles
                  : 'First Aid certification is required'}
              </p>
            )}
          </div>

          <div className={css.formRow}>
            <BrowseFiles
              label={`Upload Passenger Assistance Training (PATS/PASS) Certificate: ${isRequired('patsCertificationFiles') ? '*' : ''} ${isRequired('patsCertificationFiles') ? '(Required if indicated \'Yes, Current\' in Step 3)' : ''}`}
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('patsCertificationFiles', files)}
              initialFiles={formik.values.patsCertificationFiles}
            />
            {formik.touched.patsCertificationFiles && formik.errors.patsCertificationFiles && (
              <p className={css.error}>
                {typeof formik.errors.patsCertificationFiles === 'string'
                  ? formik.errors.patsCertificationFiles
                  : 'PATS certification is required'}
              </p>
            )}
          </div>

          <div className={css.formRow}>
            <BrowseFiles
              label={`Upload MAVT / MAVO Certificate: ${isRequired('mavtCertificationFiles') ? '*' : ''} ${isRequired('mavtCertificationFiles') ? '(Required if indicated \'Yes, Current\' in Step 3)' : ''}`}
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('mavtCertificationFiles', files)}
              initialFiles={formik.values.mavtCertificationFiles}
            />
            {formik.touched.mavtCertificationFiles && formik.errors.mavtCertificationFiles && (
              <p className={css.error}>
                {typeof formik.errors.mavtCertificationFiles === 'string'
                  ? formik.errors.mavtCertificationFiles
                  : 'MAVT/MAVO certification is required'}
              </p>
            )}
          </div>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload Defensive Driving Course Certificate: (Optional, but recommended if indicated in Step 3)"
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('defensiveDrivingCertificationFiles', files)}
              initialFiles={formik.values.defensiveDrivingCertificationFiles}
            />
          </div>

          <div className={css.formRow}>
            <BrowseFiles
              label={`Upload State-Specific NEMT Certification/Permit: ${isRequired('stateNemtCertificationFiles') ? '*' : ''} ${isRequired('stateNemtCertificationFiles') ? '(Required if indicated \'Yes\' in Step 3)' : ''}`}
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('stateNemtCertificationFiles', files)}
              initialFiles={formik.values.stateNemtCertificationFiles}
            />
            {formik.touched.stateNemtCertificationFiles && formik.errors.stateNemtCertificationFiles && (
              <p className={css.error}>
                {typeof formik.errors.stateNemtCertificationFiles === 'string'
                  ? formik.errors.stateNemtCertificationFiles
                  : 'State NEMT certification is required'}
              </p>
            )}
          </div>
        </div>

        {/* DOT Medical Documents */}
        {(medicalData?.dotMedicalRequired === "Yes (Driving larger vehicle / Employer policy)" ||
          medicalData?.dotMedicalRequired === "Unsure") && (
          <div className={css.section}>
            <h4>DOT Medical Documents (If Applicable)</h4>

            <div className={css.formRow}>
              <BrowseFiles
                label={`Upload Scan/Photo of DOT Medical Card: ${isRequired('dotMedicalCardFiles') ? '*' : ''} ${isRequired('dotMedicalCardFiles') ? '(Required if indicated as needed & held in Step 3)' : ''}`}
                maxFiles={1}
                onUploadComplete={(files) => formik.setFieldValue('dotMedicalCardFiles', files)}
                initialFiles={formik.values.dotMedicalCardFiles}
              />
              {formik.touched.dotMedicalCardFiles && formik.errors.dotMedicalCardFiles && (
                <p className={css.error}>
                  {typeof formik.errors.dotMedicalCardFiles === 'string'
                    ? formik.errors.dotMedicalCardFiles
                    : 'DOT Medical Card is required'}
                </p>
              )}
            </div>

            {medicalData?.dotMedicalStatus === "variance" && (
              <div className={css.formRow}>
                <BrowseFiles
                  label={`Upload Proof of Medical Variance/Exemption Document: ${isRequired('medicalVarianceDocumentFiles') ? '*' : ''} ${isRequired('medicalVarianceDocumentFiles') ? '(Required if indicated in Step 3)' : ''}`}
                  maxFiles={1}
                  onUploadComplete={(files) => formik.setFieldValue('medicalVarianceDocumentFiles', files)}
                  initialFiles={formik.values.medicalVarianceDocumentFiles}
                />
                {formik.touched.medicalVarianceDocumentFiles && formik.errors.medicalVarianceDocumentFiles && (
                  <p className={css.error}>
                    {typeof formik.errors.medicalVarianceDocumentFiles === 'string'
                      ? formik.errors.medicalVarianceDocumentFiles
                      : 'Medical variance document is required'}
                  </p>
                )}
              </div>
            )}
          </div>
        )}

        {/* Vehicle Documents */}
        <div className={css.section}>
          <h4>Vehicle Documents (If Using Own Vehicle - Optional)</h4>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload Proof of Auto Insurance: (Optional) - Ensure coverage meets NEMT requirements."
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('autoInsuranceFiles', files)}
              initialFiles={formik.values.autoInsuranceFiles}
            />
          </div>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload Vehicle Registration: (Optional)"
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('vehicleRegistrationFiles', files)}
              initialFiles={formik.values.vehicleRegistrationFiles}
            />
          </div>
        </div>

        {/* Optional Supporting Documents */}
        <div className={css.section}>
          <h4>Optional Supporting Documents</h4>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload Social Security Card (Optional): - Employers verify SSN post-offer via I-9/E-Verify."
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('socialSecurityCardFiles', files)}
              initialFiles={formik.values.socialSecurityCardFiles}
            />
          </div>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload Proof of Right to Work (Optional): - Employers verify I-9 eligibility separately post-offer."
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('rightToWorkFiles', files)}
              initialFiles={formik.values.rightToWorkFiles}
            />
          </div>

          <div className={css.formRow}>
            <BrowseFiles
              label="Upload Resume (Optional):"
              maxFiles={1}
              onUploadComplete={(files) => formik.setFieldValue('resumeFiles', files)}
              initialFiles={formik.values.resumeFiles}
            />
          </div>
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 3: Medical)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              className={css.saveExitBtn}
              disabled={isLoading}
            >
              Save & Exit (Complete Later)
            </button>
            <button
              type="submit"
              className={css.continueBtn}
              disabled={isLoading}
            >
              Save & Continue (To Step 5: Consents & Review) →
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtDocuments;
