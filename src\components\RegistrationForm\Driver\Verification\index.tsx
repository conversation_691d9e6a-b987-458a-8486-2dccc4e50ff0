'use client';
import React, { useState, useEffect } from 'react';
import { getCookie } from "cookies-next";
import { toast } from 'react-toastify';
import { useRouter } from 'next/navigation';
import BrowseFiles, {FileItem} from '../../../Browse/BrowseFiles';
import css from '../driverRegistration.module.scss';

interface DriverDocumentFile extends Omit<FileItem, 'side'> {
  side?: 'front' | 'back' | null;
}

interface DriverDetails {
  driverDocumentType?: 'passport' | 'drivers_license';
  docFiles?: DriverDocumentFile[];
}

interface FormInitialValues {
  driver?: DriverDetails;
  currentStep?: number;
}

interface VerificationProps {
  onFormSubmit?: () => void;
  formInitialValues?: FormInitialValues;
}


const Verification = ({ onFormSubmit }: VerificationProps) => {
  const router = useRouter();
  const [selectedDocument, setSelectedDocument] = useState('');
  const [frontFiles, setFrontFiles] = useState<FileItem[]>([]);
  const [backFiles, setBackFiles] = useState<FileItem[]>([]);
  const [passportFiles, setPassportFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentStep, setCurrentStep] = useState(0);
  const [currentStage, setCurrentStage] = useState(2);
  const [initialFrontFiles, setInitialFrontFiles] = useState<FileItem[]>([]);
  const [initialBackFiles, setInitialBackFiles] = useState<FileItem[]>([]);
  const [initialPassportFiles, setInitialPassportFiles] = useState<FileItem[]>([]);

  useEffect(() => {
    const fetchDriverDetails = async () => {
      try {
        const token = getCookie("authToken");
        if (!token) {
          console.error('No token found in initial fetch');
          setIsLoading(false);
          return;
        }
        
        const res = await fetch(
          `${process.env.NEXT_PUBLIC_API_V1}driver/driver-details`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: `${token}`,
            },
          }
        );

        const data = await res.json();
        
        if (data?.status) {
          const driver = data?.data?.driver || {};
          setCurrentStep(data?.data?.currentStep || 0);
          setCurrentStage(data?.data?.currentStage || 2);
          
          if (driver.driverDocumentType) {
            setSelectedDocument(driver.driverDocumentType === 'passport' 
              ? 'green_card_or_passport' 
              : 'license_or_stateid');
          }
          
          if (driver.docFiles && driver.docFiles.length > 0) {
            const docFiles = driver.docFiles;
            
            if (driver.driverDocumentType === 'passport') {
              setInitialPassportFiles(docFiles);
              setPassportFiles(docFiles);
              
              setInitialFrontFiles([]);
              setInitialBackFiles([]);
              setFrontFiles([]);
              setBackFiles([]);
            } else {
              const frontDocs = docFiles.filter((doc: DriverDocumentFile) => doc.side === 'front');
              const backDocs = docFiles.filter((doc: DriverDocumentFile) => doc.side === 'back');
              
              setInitialFrontFiles(frontDocs);
              setFrontFiles(frontDocs);
              setInitialBackFiles(backDocs);
              setBackFiles(backDocs);
              
              setInitialPassportFiles([]);
              setPassportFiles([]);
            }
          }
        }
      } catch (error) {
        console.error("Failed to fetch driver details", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDriverDetails();
  }, []);

  const handleSubmit = async () => {
    let docFiles: FileItem[] = [];

    if (selectedDocument === 'license_or_stateid') {
      if (frontFiles.length === 0 || backFiles.length === 0) {
        toast.error('Please upload both front and back sides of your document');
        return;
      }
      docFiles = [...frontFiles, ...backFiles];
    } else if (selectedDocument === 'green_card_or_passport') {
      if (passportFiles.length === 0) {
        toast.error('Please upload your passport');
        return;
      }
      docFiles = [...passportFiles];
    }

    const step = 7;
    const payload = {
      ...(currentStage <= 2 && { currentStage: 2 }),
      ...(currentStage === 2 && step > currentStep && { currentStep: step }),
      driver: {
        driverDocumentType:
          selectedDocument === 'green_card_or_passport' ? 'passport' : 'drivers_license',
        docFiles,
      },
    };

    try {
      const token = getCookie("authToken");
      if (!token) {
        toast.error('Authentication token not found. Please login again.');
        return;
      }
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_V1}driver/driver-details`, {
        method: 'PUT',
        headers: {
          'Authorization': `${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const responseData = await res.json();
      if (responseData.status) {
        if (selectedDocument === 'green_card_or_passport') {
          setInitialPassportFiles([...passportFiles]);
          setFrontFiles([]);
          setBackFiles([]);
          setInitialFrontFiles([]);
          setInitialBackFiles([]);
        } else {
          setInitialFrontFiles([...frontFiles]);
          setInitialBackFiles([...backFiles]);
          setPassportFiles([]);
          setInitialPassportFiles([]);
        }

        const driverCategory = responseData?.data?.driver?.driverCategory;
        // const currentStage = responseData?.data?.currentStage;
        // const currentStep = responseData?.data?.currentStep;

        let redirectPath = "/";
        let successMessage = "Documents submitted successfully!";

        if (driverCategory !== undefined && driverCategory !== null) {
          if (driverCategory === 1) {
            redirectPath = "/category/driver";
            successMessage = "Documents submitted successfully! Redirecting to driver category...";
          } else if (driverCategory === 52) {
            redirectPath = "/category/school-bus-driver";
            successMessage = "Documents submitted successfully! Redirecting to school bus driver category...";
          } 
          // else {
          //   redirectPath = "/category/driver";
          //   successMessage = `Documents submitted successfully! Redirecting to driver category (category ID: ${driverCategory})...`;
          // }
        } else {
          successMessage = "Documents submitted successfully! Redirecting to home...";
        }
        toast.success(successMessage);

        setTimeout(() => {
          if (onFormSubmit) {
            onFormSubmit();
          }
          router.push(redirectPath);
        }, 2000);
      } else {
        toast.error('Failed to submit documents');
      }
    } catch (error) {
      console.error('Error submitting documents:', error);
      toast.error('An error occurred while submitting documents');
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }
return (
  <div className={css.commonForm}>
    <div className={css.uploadDocuments}>
      <h5>Upload Documents</h5>
      <h6>Please upload the required document. We need this to verify your driving eligibility.</h6>
    </div>

    <div className={`${css.formRow} ${css.dBlaco} ${css.mt36}`}>
      <div className={css.labelDiv}>
        <label>
          Document Type<sup>*</sup>
        </label>
      </div>

      {/* Styled Radio Buttons */}
      <ul className={`${css.checkboxList} ${css.radioList}`}>
        <li className={css.radioGroup}>
          <label className={css.radioLabel}>
            <input
              type="radio"
              name="identityDocument"
              value="license_or_stateid"
              checked={selectedDocument === 'license_or_stateid'}
              onChange={(e) => setSelectedDocument(e.target.value)}
            />
            <span className={css.checkmark}></span>
            <p>Driver&apos;s License / State ID / Green Card</p>
          </label>
        </li>
        <li className={css.radioGroup}>
          <label className={css.radioLabel}>
            <input
              type="radio"
              name="identityDocument"
              value="green_card_or_passport"
              checked={selectedDocument === 'green_card_or_passport'}
              onChange={(e) => setSelectedDocument(e.target.value)}
            />
            <span className={css.checkmark}></span>
            <p>Passport</p>
          </label>
        </li>
      </ul>

      {/* Upload Fields */}
      {selectedDocument === 'license_or_stateid' && (
        <div className={css.flexBox}>
          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label>
                Upload Document (Front)<sup>*</sup>
              </label>
            </div>
            <BrowseFiles
              label=""
              side="front"
              maxFiles={1}
              onUploadComplete={setFrontFiles}
              initialFiles={initialFrontFiles}
            />
          </div>

          <div className={css.col02}>
            <div className={css.labelDiv}>
              <label>
                Upload Document (Back)<sup>*</sup>
              </label>
            </div>
            <BrowseFiles
              label=""
              side="back"
              maxFiles={1}
              onUploadComplete={setBackFiles}
              initialFiles={initialBackFiles}
            />
          </div>
        </div>
      )}

      {selectedDocument === 'green_card_or_passport' && (
        <div className={css.col02}>
          <div className={css.labelDiv}>
            <label>
              Upload Passport<sup>*</sup>
            </label>
          </div>
          <BrowseFiles
            label=""
            maxFiles={1}
            onUploadComplete={setPassportFiles}
            initialFiles={initialPassportFiles}
          />
        </div>
      )}
    </div>

    {selectedDocument && (
      <div className={`${css.formRow} ${css.submitRow}`}>
        <button onClick={handleSubmit} className={css.submitBtn}>
          Save & Complete Profile Essentials
        </button>
      </div>
    )}
  </div>
);

};

export default Verification;
