"use client";
import React, { useEffect, useRef, useState } from "react";
import css from "./dropdown.module.scss";

export interface DropdownOption {
  value: string | number;
  label: string;
}

interface DropdownProps {
  options: DropdownOption[];
  value?: string | number | null;
  placeholder?: string;
  onChange: (value: string | number) => void;
  disabled?: boolean;
  error?: string;
  className?: string;
  name?: string;
  required?: boolean;
}

const Dropdown: React.FC<DropdownProps> = ({
  options,
  value,
  placeholder = "Select",
  onChange,
  disabled = false,
  error,
  className = "",
  name,
  required = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // Find the selected option label
  const selectedOption = options.find((option) => option.value === value);
  const displayValue = selectedOption ? selectedOption.label : placeholder;

  const handleToggle = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  const handleOptionClick = (optionValue: string | number) => {
    onChange(optionValue);
    setIsOpen(false);
  };

  return (
    <div className={`${css.dropdownWrapper} ${className}`}>
      <div 
        className={`${css.dropdown} ${error ? css.hasError : ""}`} 
        ref={dropdownRef}
      >
        <button
          type="button"
          className={`${css.dropdownToggle} ${disabled ? css.disabled : ""}`}
          onClick={handleToggle}
          disabled={disabled}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          name={name}
        >
          {displayValue}
        </button>
        
        {isOpen && !disabled && (
          <div className={css.dropdownMenu} role="listbox">
            {options.map((option) => (
              <button
                key={option.value}
                type="button"
                className={`${css.dropdownItem} ${
                  option.value === value ? css.selected : ""
                }`}
                onClick={() => handleOptionClick(option.value)}
                role="option"
                aria-selected={option.value === value}
              >
                {option.label}
              </button>
            ))}
          </div>
        )}
      </div>
      
      {error && <div className={css.error}>{error}</div>}
    </div>
  );
};

export default Dropdown;
