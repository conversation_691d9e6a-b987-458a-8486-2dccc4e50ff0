// "use client";
// import DateInput from "@/components/Common/DateInput/DateInput";
// import RadioGroup from "@/components/Common/RadioGroup";
// import {
//   fetchLanguages,
//   fetchSchoolBusAideFormFields,
//   FormValue,
//   submitDriverDetails,
//   fetchDriverDetails,
//   FetchDriverDetailsResponse
// } from "@/services/driverFormService";
// import { useFormik } from "formik";
// import { useEffect, useState } from "react";
// import { toast } from "react-toastify";
// import * as Yup from "yup";

// export interface SkillsCertificationsFormValues {
//   hasCPRCertification: string;
//   cprCertificationType: string;
//   cprExpirationDate: Date | null;
//   hasFirstAidCertification: string;
//   firstAidCertificationType: string;
//   firstAidExpirationDate: Date | null;
//   hasPATS: string;
//   patsCertificationDate: Date | null;
//   issuingAgency: string;
//   hasBehaviorTraining: string;
//   behaviorTrainingType: string;
//   behaviorTrainingDate: Date | null;
//   stateClearancesStatus: string;
//   stateClearancesDetails: string;
//   primaryLanguage: string;
//   otherLanguages: string;
//   otherLanguageText: string;
// }
// type DriverDetails = FetchDriverDetailsResponse["data"]["driver"] & SkillsCertificationsFormValues;

// const SkillsCertificate: React.FC = () => {
//   const [certificationType, setCertificationType] = useState<FormValue[]>([]);
//   const [aidCertification, setAidCertification] = useState<FormValue[]>([]);
//   const [assistanceTrainingOptions, setAssistanceTrainingOptions] = useState<FormValue[]>([]);
//   const [stateClearances, setStateClearances] = useState<FormValue[]>([]);
//   // const [primaryLanguages, setPrimaryLanguages] = useState<FormValue[]>([]);

//   useEffect(() => {
//     const loadData = async () => {
//       const formFieldData = await fetchSchoolBusAideFormFields();
//       setCertificationType(formFieldData["cpr-certification-type-driver-bus-aide-assistant"]);
//       setAidCertification(formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]);
//       setAssistanceTrainingOptions(formFieldData["passenger-assistance-training-pats-ctaa-pass-driver-bus-aide-assistant"]);
//       setStateClearances(formFieldData["state-required-clearances-training-for-school-personnel-driver-bus-aide-assistant"]);
//       // const getLanguages = await fetchLanguages();
//       // setPrimaryLanguages(getLanguages);
//     };
//     loadData();
//   }, []);

//   const formik = useFormik<SkillsCertificationsFormValues>({
//     initialValues: {
//       hasCPRCertification: "Yes",
//       cprCertificationType: "",
//       cprExpirationDate: null,
//       hasFirstAidCertification: "Yes",
//       firstAidCertificationType: "",
//       firstAidExpirationDate: null,
//       hasPATS: "Yes",
//       patsCertificationDate: null,
//       issuingAgency: "",
//       hasBehaviorTraining: "Yes",
//       behaviorTrainingType: "",
//       behaviorTrainingDate: null,
//       stateClearancesStatus: "",
//       stateClearancesDetails: "",
//       primaryLanguage: "",
//       otherLanguages: "",
//       otherLanguageText: "",
//     },
//     validationSchema: Yup.object().shape({
//       otherLanguageText: Yup.string().nullable(),
//     }),
//   onSubmit: async (values) => {
//   const getBoolean = (val: string) =>
//     val === "Yes" ? true : val?.toLowerCase().includes("no") ? false : null;

//   const getDate = (val: Date | null) =>
//     val ? new Date(val).toISOString() : null;

//   const getNumber = (val: string) =>
//     val && !isNaN(+val) ? Number(val) : null;

//   // --- Fix: Find formValueId for selected label ---
//   const selectedClearance = stateClearances.find(
//     (item) => item.label.en === values.stateClearancesStatus
//   );
//   const clearanceId = selectedClearance ? selectedClearance.formValueId : null;

//   const patsCert = values.hasPATS?.toLowerCase().includes("yes") ? true : false;

//   const payload = {
//     currentStage: 3,
//     currentStep: 3,
//     driver: {
//       cprCertification: getBoolean(values.hasCPRCertification),
//       cprCertificationType: getNumber(values.cprCertificationType),
//       cprCertificationExpirationDate: getDate(values.cprExpirationDate),

//       firstAidCertification: getBoolean(values.hasFirstAidCertification),
//       firstAidCertificationType: getNumber(values.firstAidCertificationType),
//       firstAidCertificationExpirationDate: getDate(values.firstAidExpirationDate),

//       patsCertification: patsCert,
//       patsCertificationDate: patsCert ? getDate(values.patsCertificationDate) : null,
//       patsCertificateIssuingAgency: values.issuingAgency || null,

//       behaviorTraining: getBoolean(values.hasBehaviorTraining),
//       behaviorTrainingName: values.behaviorTrainingType || null,
//       behaviorTrainingCompletedDate: getDate(values.behaviorTrainingDate),

//       childAbuseClearance: clearanceId,
//       childAbuseClearanceHeld: values.stateClearancesDetails || null,
//     },
//   };

//   const success = await submitDriverDetails(payload);
//   if (success) {
//     toast.success("Submitted successfully!");
//   } else {
//     toast.error("Something went wrong");
//   }
// }

//   })

// const handleSubmit =async()=>{

// }

//   // Step 5: Fetch data and map to form
//   useEffect(() => {
//     const loadDriverDetails = async () => {
//       try {
//         const res = await fetchDriverDetails();
//         const driver = res?.data.driver as DriverDetails

//         const findLabelById = (list: FormValue[], id: number | null) =>
//           list.find((item) => item.formValueId === id)?.label.en || "";

//         formik.setValues({
//           hasCPRCertification: driver.cprCertification ? "Yes" : "No",
//           cprCertificationType: driver.cprCertificationType?.toString() || "",
//           cprExpirationDate: driver.cprCertificationExpirationDate
//             ? new Date(driver.cprCertificationExpirationDate)
//             : null,

//           hasFirstAidCertification: driver.firstAidCertification ? "Yes" : "No",
//           firstAidCertificationType: driver.firstAidCertificationType?.toString() || "",
//           firstAidExpirationDate: driver.firstAidCertificationExpirationDate
//             ? new Date(driver.firstAidCertificationExpirationDate)
//             : null,

//           hasPATS: driver.patsCertification ? "Yes" : "No",
//           patsCertificationDate: driver.patsCertificationDate
//             ? new Date(driver.patsCertificationDate)
//             : null,
//           issuingAgency: driver.patsCertificateIssuingAgency || "",

//           hasBehaviorTraining: driver.behaviorTraining ? "Yes" : "No",
//           behaviorTrainingType: driver.behaviorTrainingName || "",
//           behaviorTrainingDate: driver.behaviorTrainingCompletedDate
//             ? new Date(driver.behaviorTrainingCompletedDate)
//             : null,

//           stateClearancesStatus: findLabelById(stateClearances, driver.childAbuseClearance),
//           stateClearancesDetails: driver.childAbuseClearanceHeld || "",

//           primaryLanguage: "",
//           otherLanguages: "",
//           otherLanguageText: "",
//         });
//       } catch (err) {
//         console.error("Failed to load driver details", err);
//       }
//     };
//     loadDriverDetails();
//   }, [stateClearances])
//   return (
//     <div style={{ padding: "2rem", maxWidth: "1200px", margin: "0 auto" }}>
//       <h3 style={{ fontSize: "1.5rem", marginBottom: "0.5rem" }}>
//         Skills & Certifications (Bus Aide / Bus Assistant)
//       </h3>
//       <p style={{ marginBottom: "1.5rem" }}>
//         Required fields are marked with *
//       </p>

//       <form onSubmit={formik.handleSubmit}>
//         {/* CPR Section */}
//         <div className="form-section" style={{ marginBottom: "2rem" }}>
//           <label style={{ display: "block", marginBottom: "0.5rem" }}>
//             Current CPR Certification? *
//           </label>
//           <RadioGroup
//             name="hasCPRCertification"
//             selectedValue={formik.values.hasCPRCertification}
//             onChange={(value) =>
//               formik.setFieldValue("hasCPRCertification", value)
//             }
//             options={["Yes", "No / Expired"]}
//           />
//           {formik.values.hasCPRCertification === "Yes" && (
//             <>
//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 CPR Certification Type *
//               </label>
//               <select
//                 name="cprCertificationType"
//                 value={formik.values.cprCertificationType}
//                 onChange={formik.handleChange}
//                 style={{
//                   padding: "0.5rem",
//                   marginTop: "0.5rem",
//                   width: "100%",
//                 }}
//               >
//                 <option value="">Select Type</option>
//                 {certificationType.map((item) => (
//                   <option key={item.formValueId} value={item.formValueId}>
//                     {item.label.en}
//                   </option>
//                 ))}
//               </select>

//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 CPR Expiration Date *
//               </label>
//               <DateInput
//                 selected={formik.values.cprExpirationDate}
//                 onChange={(date) =>
//                   formik.setFieldValue("cprExpirationDate", date)
//                 }
//               />
//             </>
//           )}
//         </div>

//         {/* First Aid Section */}
//         <div className="form-section" style={{ marginBottom: "2rem" }}>
//           <label style={{ display: "block", marginBottom: "0.5rem" }}>
//             Current First Aid Certification? *
//           </label>
//           <RadioGroup
//             name="hasFirstAidCertification"
//             selectedValue={formik.values.hasFirstAidCertification}
//             onChange={(value) =>
//               formik.setFieldValue("hasFirstAidCertification", value)
//             }
//             options={["Yes", "No / Expired"]}
//           />
//           {formik.values.hasFirstAidCertification === "Yes" && (
//             <>
//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 First Aid Certification Type *
//               </label>
//               <select
//                 name="firstAidCertificationType"
//                 value={formik.values.firstAidCertificationType}
//                 onChange={formik.handleChange}
//                 style={{
//                   padding: "0.5rem",
//                   marginTop: "0.5rem",
//                   width: "100%",
//                 }}
//               >
//                 <option value="">Select Type</option>
//                 {aidCertification.map((item) => (
//                   <option key={item.formValueId} value={item.formValueId}>
//                     {item.label.en}
//                   </option>
//                 ))}
//               </select>

//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 First Aid Expiration Date *
//               </label>
//               <DateInput
//                 selected={formik.values.firstAidExpirationDate}
//                 onChange={(date) =>
//                   formik.setFieldValue("firstAidExpirationDate", date)
//                 }
//               />
//             </>
//           )}
//         </div>

//         {/* PATS Section */}
//         <div className="form-section" style={{ marginBottom: "2rem" }}>
//           <label style={{ display: "block", marginBottom: "0.5rem" }}>
//             PATS / PASS Certification?
//           </label>
//           <RadioGroup
//             name="hasPATS"
//             selectedValue={formik.values.hasPATS}
//             onChange={(value) => formik.setFieldValue("hasPATS", value)}
//             options={assistanceTrainingOptions.map((option) => option.label.en)}
//           />
//           {formik.values.hasPATS.includes("Yes") && (
//             <>
//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 Certification Date (Optional)
//               </label>
//               <DateInput
//                 selected={formik.values.patsCertificationDate}
//                 onChange={(date) =>
//                   formik.setFieldValue("patsCertificationDate", date)
//                 }
//               />
//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 Issuing Agency/Trainer (Optional)
//               </label>
//               <input
//                 type="text"
//                 name="issuingAgency"
//                 value={formik.values.issuingAgency}
//                 onChange={formik.handleChange}
//                 style={{
//                   width: "100%",
//                   padding: "0.5rem",
//                   marginTop: "0.5rem",
//                 }}
//               />
//             </>
//           )}
//         </div>

//         {/* Behavior Section */}
//         <div className="form-section" style={{ marginBottom: "2rem" }}>
//           <label style={{ display: "block", marginBottom: "0.5rem" }}>
//             Behavior Management Training?
//           </label>
//           <RadioGroup
//             name="hasBehaviorTraining"
//             selectedValue={formik.values.hasBehaviorTraining}
//             onChange={(value) =>
//               formik.setFieldValue("hasBehaviorTraining", value)
//             }
//             options={["Yes", "No / Not Formally Trained"]}
//           />
//           {formik.values.hasBehaviorTraining === "Yes" && (
//             <>
//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 Training Type/Name
//               </label>
//               <input
//                 type="text"
//                 name="behaviorTrainingType"
//                 value={formik.values.behaviorTrainingType}
//                 onChange={formik.handleChange}
//                 style={{
//                   width: "100%",
//                   padding: "0.5rem",
//                   marginTop: "0.5rem",
//                 }}
//               />
//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 Date Completed (Optional)
//               </label>
//               <DateInput
//                 selected={formik.values.behaviorTrainingDate}
//                 onChange={(date) =>
//                   formik.setFieldValue("behaviorTrainingDate", date)
//                 }
//               />
//             </>
//           )}
//         </div>

//         {/* State Clearance */}
//         <div className="form-section" style={{ marginBottom: "2rem" }}>
//           <label style={{ display: "block", marginBottom: "0.5rem" }}>
//             State-Required Clearances / Training for School Personnel
//           </label>
//           <RadioGroup
//             name="stateClearancesStatus"
//             selectedValue={formik.values.stateClearancesStatus}
//             onChange={(value) =>
//               formik.setFieldValue("stateClearancesStatus", value)
//             }
//             options={stateClearances.map((options) => options.label.en)}
//           />
//           {(formik.values.stateClearancesStatus === "Yes, All Current" ||
//             formik.values.stateClearancesStatus ===
//               "Some Completed / Need Updates") && (
//             <>
//               <label style={{ display: "block", marginTop: "1rem" }}>
//                 Specify Clearances/Training Held
//               </label>
//               <textarea
//                 name="stateClearancesDetails"
//                 value={formik.values.stateClearancesDetails}
//                 onChange={formik.handleChange}
//                 style={{
//                   width: "100%",
//                   padding: "0.5rem",
//                   marginTop: "0.5rem",
//                   minHeight: "80px",
//                 }}
//               />
//             </>
//           )}
//         </div>

//         {/* Language Skills
//         <div className="form-section" style={{ marginBottom: "2rem" }}>
//           <label style={{ display: "block", marginBottom: "0.5rem" }}>
//             Primary Language *
//           </label>
//           <select
//             name="primaryLanguage"
//             value={formik.values.primaryLanguage}
//             onChange={formik.handleChange}
//             style={{ width: "100%", padding: "0.5rem" }}
//           >
//             {primaryLanguages.map((item) => (
//               <option value={item.formValueId} key={item.formValueId}>
//                 {item.label.en}
//               </option>
//             ))}
//           </select>

//           <label style={{ display: "block", marginTop: "1rem" }}>
//             Other Languages Spoken (Optional)
//           </label> */}
//           {/* <div
//             style={{
//               display: "flex",
//               flexDirection: "column",
//               gap: "0.5rem",
//               marginTop: "0.5rem",
//             }}
//           >
//             <label>
//               <input type="checkbox" name="otherLanguages" value="Spanish" />{" "}
//               Spanish
//             </label>
//             <label>
//               <input type="checkbox" name="otherLanguages" value="ASL" /> ASL
//             </label>
//             <label>
//               <input
//                 type="checkbox"
//                 name="otherLanguages"
//                 value="Other"
//                 onChange={formik.handleChange}
//               />
//               Other:{" "}
//               <input
//                 type="text"
//                 name="otherLanguageText"
//                 value={formik.values.otherLanguageText}
//                 onChange={formik.handleChange}
//                 style={{ marginLeft: "0.5rem", padding: "0.25rem" }}
//               />
//             </label>
//             <label>
//               <input type="checkbox" name="otherLanguages" value="None" /> None
//             </label>
//           </div> */}
//         {/* </div> */}
//         <button type="submit">Submit Details </button>
//       </form>
//     </div>
//   );
// };

// export default SkillsCertificate;

"use client";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import { useSchoolBusAideCategory } from "@/contexts/CommonDriverCategoryContext";
import {
 
  fetchSchoolBusAideFormFields,
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  FetchDriverDetailsResponse,
} from "@/services/driverFormService";
import { useFormik } from "formik";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import * as Yup from "yup";

export interface SkillsCertificationsFormValues {
  hasCPRCertification: string;
  cprCertificationType: string;
  cprExpirationDate: Date | null;
  hasFirstAidCertification: string;
  firstAidCertificationType: string;
  firstAidExpirationDate: Date | null;
  hasPATS: string;
  patsCertificationDate: Date | null;
  issuingAgency: string;
  hasBehaviorTraining: string;
  behaviorTrainingType: string;
  behaviorTrainingDate: Date | null;
  stateClearancesStatus: string;
  stateClearancesDetails: string;
  primaryLanguage: string;
  otherLanguages: string;
  otherLanguageText: string;
}
type DriverDetails = FetchDriverDetailsResponse["data"]["driver"] &
  SkillsCertificationsFormValues;

const SkillsCertificate: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } =
    useSchoolBusAideCategory();
  const router = useRouter();

  const [certificationType, setCertificationType] = useState<FormValue[]>([]);
  const [aidCertification, setAidCertification] = useState<FormValue[]>([]);
  const [assistanceTrainingOptions, setAssistanceTrainingOptions] = useState<
    FormValue[]
  >([]);
  const [stateClearances, setStateClearances] = useState<FormValue[]>([]);
  // const [primaryLanguages, setPrimaryLanguages] = useState<FormValue[]>([]);
  // const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      const formFieldData = await fetchSchoolBusAideFormFields();
      setCertificationType(
        formFieldData["cpr-certification-type-driver-bus-aide-assistant"]
      );
      setAidCertification(
        formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]
      );
      setAssistanceTrainingOptions(
        formFieldData[
          "passenger-assistance-training-pats-ctaa-pass-driver-bus-aide-assistant"
        ]
      );
      setStateClearances(
        formFieldData[
          "state-required-clearances-training-for-school-personnel-driver-bus-aide-assistant"
        ]
      );
      // const getLanguages = await fetchLanguages();
      // setPrimaryLanguages(getLanguages);
    };
    loadData();
  }, []);

  const formik = useFormik<SkillsCertificationsFormValues>({
    initialValues: {
      hasCPRCertification: "Yes",
      cprCertificationType: "",
      cprExpirationDate: null,
      hasFirstAidCertification: "Yes",
      firstAidCertificationType: "",
      firstAidExpirationDate: null,
      hasPATS: "Yes",
      patsCertificationDate: null,
      issuingAgency: "",
      hasBehaviorTraining: "Yes",
      behaviorTrainingType: "",
      behaviorTrainingDate: null,
      stateClearancesStatus: "",
      stateClearancesDetails: "",
      primaryLanguage: "",
      otherLanguages: "",
      otherLanguageText: "",
    },
    enableReinitialize:true,
    validationSchema: Yup.object().shape({
      otherLanguageText: Yup.string().nullable(),
    }),
    onSubmit: async (values) => {
      handleSubmit(values, true);
    },
  });

  const handleSubmit = async (
    values: SkillsCertificationsFormValues,
    shouldContinue: boolean = true
  ) => {

    try {
      const getBoolean = (val: string) =>
        val === "Yes" ? true : val?.toLowerCase().includes("no") ? false : null;

      const getDate = (val: Date | null) =>
        val ? new Date(val).toISOString() : null;

      const getNumber = (val: string) =>
        val && !isNaN(+val) ? Number(val) : null;

      // --- Fix: Find formValueId for selected label ---
      const selectedClearance = stateClearances.find(
        (item) => item.label.en === values.stateClearancesStatus
      );
      const clearanceId = selectedClearance
        ? selectedClearance.formValueId
        : null;

      const patsCert = values.hasPATS?.toLowerCase().includes("yes")
        ? true
        : false;

      const payload = {
        currentStage: 3,
        currentStep: 3,
        driver: {
          cprCertification: getBoolean(values.hasCPRCertification),
          cprCertificationType: getNumber(values.cprCertificationType),
          cprCertificationExpirationDate: getDate(values.cprExpirationDate),

          firstAidCertification: getBoolean(values.hasFirstAidCertification),
          firstAidCertificationType: getNumber(
            values.firstAidCertificationType
          ),
          firstAidCertificationExpirationDate: getDate(
            values.firstAidExpirationDate
          ),

          patsCertification: patsCert,
          patsCertificationDate: patsCert
            ? getDate(values.patsCertificationDate)
            : null,
          patsCertificateIssuingAgency: values.issuingAgency || null,

          behaviorTraining: getBoolean(values.hasBehaviorTraining),
          behaviorTrainingName: values.behaviorTrainingType || null,
          behaviorTrainingCompletedDate: getDate(values.behaviorTrainingDate),

          childAbuseClearance: clearanceId,
          childAbuseClearanceHeld: values.stateClearancesDetails || null,
        },
        
      };
      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Skilla information saved successfully!");
          window.scrollTo({ top: 0, behavior: "smooth" });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage =
          response?.message ||
          response?.error?.message ||
          "Failed to save medical information. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting Skills information:", error);
      toast.error("Failed to save Skils=ls information. Please try again.");
    } finally {
 
    }
  };

  // Step 5: Fetch data and map to form
  useEffect(() => {
    const loadDriverDetails = async () => {
      try {
        const res = await fetchDriverDetails();
        const driver = res?.data.driver as DriverDetails;
          if (!driver) return;
        const findLabelById = (list: FormValue[], id: number | null) =>
          list.find((item) => item.formValueId === id)?.label.en || "";

        formik.setValues({
          hasCPRCertification: driver.cprCertification ? "Yes" : "No",
          cprCertificationType: driver.cprCertificationType?.toString() || "",
          cprExpirationDate: driver.cprCertificationExpirationDate
            ? new Date(driver.cprCertificationExpirationDate)
            : null,

          hasFirstAidCertification: driver.firstAidCertification ? "Yes" : "No",
          firstAidCertificationType:
            driver.firstAidCertificationType?.toString() || "",
          firstAidExpirationDate: driver.firstAidCertificationExpirationDate
            ? new Date(driver.firstAidCertificationExpirationDate)
            : null,

          hasPATS: driver.patsCertification ? "Yes" : "No",
          patsCertificationDate: driver.patsCertificationDate
            ? new Date(driver.patsCertificationDate)
            : null,
          issuingAgency: driver.patsCertificateIssuingAgency || "",

          hasBehaviorTraining: driver.behaviorTraining ? "Yes" : "No",
          behaviorTrainingType: driver.behaviorTrainingName || "",
          behaviorTrainingDate: driver.behaviorTrainingCompletedDate
            ? new Date(driver.behaviorTrainingCompletedDate)
            : null,

          stateClearancesStatus: findLabelById(
            stateClearances,
            driver.childAbuseClearance
          ),
          stateClearancesDetails: driver.childAbuseClearanceHeld || "",

          primaryLanguage: "",
          otherLanguages: "",
          otherLanguageText: "",
        });
      } catch (err) {
        console.error("Failed to load driver details", err);
      }
    };
    loadDriverDetails();
  }, [stateClearances]);
  
  return (
    <div style={{ padding: "2rem", maxWidth: "1200px", margin: "0 auto" }}>
      <h3 style={{ fontSize: "1.5rem", marginBottom: "0.5rem" }}>
        Skills & Certifications (Bus Aide / Bus Assistant)
      </h3>
      <p style={{ marginBottom: "1.5rem" }}>
        Required fields are marked with *
      </p>

      <form onSubmit={formik.handleSubmit}>
        {/* CPR Section */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            Current CPR Certification? *
          </label>
          <RadioGroup
            name="hasCPRCertification"
            selectedValue={formik.values.hasCPRCertification}
            onChange={(value) =>
              formik.setFieldValue("hasCPRCertification", value)
            }
            options={["Yes", "No / Expired"]}
          />
          {formik.values.hasCPRCertification === "Yes" && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                CPR Certification Type *
              </label>
              <Dropdown
                options={certificationType.map(item => ({ value: item.formValueId, label: item.label.en }))}
                value={formik.values.cprCertificationType}
                placeholder="Select Type"
                onChange={(value) => formik.setFieldValue("cprCertificationType", value)}
                name="cprCertificationType"
              />

              <label style={{ display: "block", marginTop: "1rem" }}>
                CPR Expiration Date *
              </label>
              <DateInput
                selected={formik.values.cprExpirationDate}
                onChange={(date) =>
                  formik.setFieldValue("cprExpirationDate", date)
                }
              />
            </>
          )}
        </div>

        {/* First Aid Section */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            Current First Aid Certification? *
          </label>
          <RadioGroup
            name="hasFirstAidCertification"
            selectedValue={formik.values.hasFirstAidCertification}
            onChange={(value) =>
              formik.setFieldValue("hasFirstAidCertification", value)
            }
            options={["Yes", "No / Expired"]}
          />
          {formik.values.hasFirstAidCertification === "Yes" && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                First Aid Certification Type *
              </label>
              <Dropdown
                options={aidCertification.map(item => ({ value: item.formValueId, label: item.label.en }))}
                value={formik.values.firstAidCertificationType}
                placeholder="Select Type"
                onChange={(value) => formik.setFieldValue("firstAidCertificationType", value)}
                name="firstAidCertificationType"
              />

              <label style={{ display: "block", marginTop: "1rem" }}>
                First Aid Expiration Date *
              </label>
              <DateInput
                selected={formik.values.firstAidExpirationDate}
                onChange={(date) =>
                  formik.setFieldValue("firstAidExpirationDate", date)
                }
              />
            </>
          )}
        </div>

        {/* PATS Section */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            PATS / PASS Certification?
          </label>
          <RadioGroup
            name="hasPATS"
            selectedValue={formik.values.hasPATS}
            onChange={(value) => formik.setFieldValue("hasPATS", value)}
            options={assistanceTrainingOptions.map((option) => option.label.en)}
          />
          {formik.values.hasPATS.includes("Yes") && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                Certification Date (Optional)
              </label>
              <DateInput
                selected={formik.values.patsCertificationDate}
                onChange={(date) =>
                  formik.setFieldValue("patsCertificationDate", date)
                }
              />
              <label style={{ display: "block", marginTop: "1rem" }}>
                Issuing Agency/Trainer (Optional)
              </label>
              <input
                type="text"
                name="issuingAgency"
                value={formik.values.issuingAgency}
                onChange={formik.handleChange}
                style={{
                  width: "100%",
                  padding: "0.5rem",
                  marginTop: "0.5rem",
                }}
              />
            </>
          )}
        </div>

        {/* Behavior Section */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            Behavior Management Training?
          </label>
          <RadioGroup
            name="hasBehaviorTraining"
            selectedValue={formik.values.hasBehaviorTraining}
            onChange={(value) =>
              formik.setFieldValue("hasBehaviorTraining", value)
            }
            options={["Yes", "No / Not Formally Trained"]}
          />
          {formik.values.hasBehaviorTraining === "Yes" && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                Training Type/Name
              </label>
              <input
                type="text"
                name="behaviorTrainingType"
                value={formik.values.behaviorTrainingType}
                onChange={formik.handleChange}
                style={{
                  width: "100%",
                  padding: "0.5rem",
                  marginTop: "0.5rem",
                }}
              />
              <label style={{ display: "block", marginTop: "1rem" }}>
                Date Completed (Optional)
              </label>
              <DateInput
                selected={formik.values.behaviorTrainingDate}
                onChange={(date) =>
                  formik.setFieldValue("behaviorTrainingDate", date)
                }
              />
            </>
          )}
        </div>

        {/* State Clearance */}
        <div className="form-section" style={{ marginBottom: "2rem" }}>
          <label style={{ display: "block", marginBottom: "0.5rem" }}>
            State-Required Clearances / Training for School Personnel
          </label>
          <RadioGroup
            name="stateClearancesStatus"
            selectedValue={formik.values.stateClearancesStatus}
            onChange={(value) =>
              formik.setFieldValue("stateClearancesStatus", value)
            }
            options={stateClearances.map((options) => options.label.en)}
          />
          {(formik.values.stateClearancesStatus === "Yes, All Current" ||
            formik.values.stateClearancesStatus ===
              "Some Completed / Need Updates") && (
            <>
              <label style={{ display: "block", marginTop: "1rem" }}>
                Specify Clearances/Training Held
              </label>
              <textarea
                name="stateClearancesDetails"
                value={formik.values.stateClearancesDetails}
                onChange={formik.handleChange}
                style={{
                  width: "100%",
                  padding: "0.5rem",
                  marginTop: "0.5rem",
                  minHeight: "80px",
                }}
              />
            </>
          )}
        </div>

        {/* Navigation Buttons */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginTop: "2rem",
            padding: "1.5rem",
            borderTop: "1px solid #e5e5e5",
          }}
        >
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              // disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#6c757d",
                color: "white",
                border: "none",
                borderRadius: "4px",
                // cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              &lt; Back  
            </button>
          )}

          <div style={{ display: "flex", gap: "1rem" }}>
            <button
              type="submit"
              // disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                borderRadius: "4px",
                // cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              Save and continue
              {/* {isLoading ? "Saving..." : "Save & Continue (To Step 4: Docs) >"} */}
            </button>

            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              // disabled={isLoading}
              style={{
                padding: "0.75rem 1.5rem",
                backgroundColor: "#28a745",
                color: "white",
                border: "none",
                borderRadius: "4px",
                // cursor: isLoading ? "not-allowed" : "pointer",
                fontSize: "16px",
              }}
            >
              Save & Exit (Complete Later)
            </button>
          </div>
        </div>
        {/* <button type="submit">Submit Details </button> */}
      </form>
    </div>
  );
};

export default SkillsCertificate;
