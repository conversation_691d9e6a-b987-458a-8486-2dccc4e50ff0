"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import { fetchNemtDriverFormFields, FormValue, submitDriverDetails, fetchDriverDetails, FetchDriverDetailsResponse } from "@/services/driverFormService";
import CheckboxGroup from "@/components/Common/CheckboxGroup";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtDriverExperience.module.scss';

interface NemtExperienceFormValues {
  nemtDrivingExperience: string;
  totalDrivingExperience: string;
  vehicleTypes: number[];
  otherVehicleType: string;
  passengerTypes: number[];
  assistanceSkills: number[];
  tripTypes: number[];
  transmissionTypes: number[];
  additionalSkills: number[];
}

interface NemtDriverData {
  totalVerifiableNemtDrivingExperience?: number;
  totalDrivingExperience?: number;
  nemtVehicleTypes?: number[];
  otherVehicleType?: string;
  passengerTypes?: number[];
  assistanceSkills?: number[];
  tripTypes?: number[];
  operationalExperience?: number[];
  transmissionTypes?: number[];
  additionalSkills?: number[];
}

type DriverWithNemtData = FetchDriverDetailsResponse["data"]["driver"] & NemtDriverData;

const Experience: React.FC = () => {
  const { updateStepFromApiResponse } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoading, setIsDataLoading] = useState(true);

  const [vehicleTypeOptions, setVehicleTypeOptions] = useState<FormValue[]>([]);
  const [passengerTypeOptions, setPassengerTypeOptions] = useState<FormValue[]>([]);
  const [assistanceSkillOptions, setAssistanceSkillOptions] = useState<FormValue[]>([]);
  const [tripTypeOptions, setTripTypeOptions] = useState<FormValue[]>([]);
  const [transmissionOptions, setTransmissionOptions] = useState<FormValue[]>([]);
  const [additionalSkillsOptions, setAdditionalSkillsOptions] = useState<FormValue[]>([]);

  const experienceYears = [
    "< 1 Year",
    "1 Year", 
    "2 Years",
    "3 Years",
    "4 Years", 
    "5 Years",
    "6 - 10 Years",
    "10+ Years"
  ];

  useEffect(() => {
    const loadFormFields = async () => {
      setIsDataLoading(true);
      try {
        const [formFieldsResponse, driverDetails] = await Promise.all([
          fetchNemtDriverFormFields([
            "types-of-nemt-vehicles-driven-driver-non-emergency-medical-transportation",
            "types-of-passengers-transported-driver-non-emergency-medical-transportation",
            "assistance-skills-driver-non-emergency-medical-transportation",
            "trip-types-destinations-driver-non-emergency-medical-transportation",
            "transmission-type-experience-driver-school-bus-driver",
            "additional-skills-experience-driver-school-bus-driver"
          ]),
          fetchDriverDetails()
        ]);

        console.log("**formFieldsResponse", formFieldsResponse);

        if (formFieldsResponse?.status && formFieldsResponse?.data) {
          const formFields = formFieldsResponse.data;
          console.log("**formFields object", formFields);
          const vehicleFieldKey = "types-of-nemt-vehicles-driven-driver-non-emergency-medical-transportation";
          console.log("**Looking for vehicleFieldKey:", vehicleFieldKey);
          console.log("**Available keys in formFields:", Object.keys(formFields));
          if (formFields[vehicleFieldKey]?.formValues) {
            console.log("**vehicleField.formValues", formFields[vehicleFieldKey].formValues);
            setVehicleTypeOptions(formFields[vehicleFieldKey].formValues);
          } else {
            console.log("**vehicleField not found or no formValues");
          }

          const passengerFieldKey = "types-of-passengers-transported-driver-non-emergency-medical-transportation";
          if (formFields[passengerFieldKey]?.formValues) {
            setPassengerTypeOptions(formFields[passengerFieldKey].formValues);
          }

          const assistanceFieldKey = "assistance-skills-driver-non-emergency-medical-transportation";
          if (formFields[assistanceFieldKey]?.formValues) {
            setAssistanceSkillOptions(formFields[assistanceFieldKey].formValues);
          }

          const tripFieldKey = "trip-types-destinations-driver-non-emergency-medical-transportation";
          if (formFields[tripFieldKey]?.formValues) {
            setTripTypeOptions(formFields[tripFieldKey].formValues);
          }

          const transmissionFieldKey = "transmission-type-experience-driver-school-bus-driver";
          if (formFields[transmissionFieldKey]?.formValues) {
            setTransmissionOptions(formFields[transmissionFieldKey].formValues);
          }

          const additionalFieldKey = "additional-skills-experience-driver-school-bus-driver";
          if (formFields[additionalFieldKey]?.formValues) {
            setAdditionalSkillsOptions(formFields[additionalFieldKey].formValues);
          }
        }
        if (driverDetails?.status && driverDetails?.data?.driver) {
          const driver = driverDetails.data.driver as DriverWithNemtData;

          console.log("Raw driver data from API:", {
            nemtVehicleTypes: driver.nemtVehicleTypes,
            passengerTypes: driver.passengerTypes,
            assistanceSkills: driver.assistanceSkills,
            tripTypes: driver.tripTypes,
            operationalExperience: driver.operationalExperience,
            transmissionTypes: driver.transmissionTypes,
            additionalSkills: driver.additionalSkills
          });

          const experienceReverseMapping: Record<number, string> = {
            0: "< 1 Year",
            1: "1 Year",
            2: "2 Years",
            3: "3 Years",
            4: "4 Years",
            5: "5 Years",
            6: "6 - 10 Years",
            10: "10+ Years"
          };

          const cleanArrayToNumbers = (arr: (string | number)[]): number[] => {
            if (!Array.isArray(arr)) return [];
            const cleaned = [...new Set(arr.map(item => Number(item)).filter(num => !isNaN(num)))];
            console.log(`Cleaning array [${arr}] -> [${cleaned}]`);
            return cleaned;
          };

          const cleanedValues = {
            nemtDrivingExperience: driver.totalVerifiableNemtDrivingExperience !== undefined
              ? experienceReverseMapping[driver.totalVerifiableNemtDrivingExperience] || ""
              : "",
            totalDrivingExperience: driver.totalDrivingExperience !== undefined
              ? experienceReverseMapping[driver.totalDrivingExperience] || ""
              : "",
            vehicleTypes: cleanArrayToNumbers(driver.nemtVehicleTypes || []),
            otherVehicleType: driver.otherVehicleType || "",
            passengerTypes: cleanArrayToNumbers(driver.passengerTypes || []),
            assistanceSkills: cleanArrayToNumbers(driver.assistanceSkills || []),
            tripTypes: cleanArrayToNumbers(driver.tripTypes || []),
            transmissionTypes: cleanArrayToNumbers(driver.transmissionTypes || []),
            additionalSkills: cleanArrayToNumbers(driver.additionalSkills || [])
          };
          formik.setValues(cleanedValues);
        }

      } catch (error) {
        console.error("Failed to load form fields:", error);
        toast.error("Failed to load form options. Please refresh the page.");
      } finally {
        setIsDataLoading(false);
      }
    };

    loadFormFields();
  }, []);

  const validationSchema = Yup.object().shape({
    nemtDrivingExperience: Yup.string().required("NEMT driving experience is required"),
    vehicleTypes: Yup.array().min(1, "Please select at least one vehicle type"),
    passengerTypes: Yup.array().min(1, "Please select at least one passenger type"),
    assistanceSkills: Yup.array().min(1, "Please select at least one assistance skill"),
    tripTypes: Yup.array().min(1, "Please select at least one trip type"),
    transmissionTypes: Yup.array().min(1, "Please select at least one transmission type"),
  });

  const formik = useFormik<NemtExperienceFormValues>({
    initialValues: {
      nemtDrivingExperience: "",
      totalDrivingExperience: "",
      vehicleTypes: [],
      otherVehicleType: "",
      passengerTypes: [],
      assistanceSkills: [],
      tripTypes: [],
      transmissionTypes: [],
      additionalSkills: [],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  const handleSubmit = async (values: NemtExperienceFormValues, shouldContinue: boolean = true) => {
    setIsLoading(true);
    try {
      const experienceMapping: Record<string, number> = {
        "< 1 Year": 0,
        "1 Year": 1,
        "2 Years": 2,
        "3 Years": 3,
        "4 Years": 4,
        "5 Years": 5,
        "6 - 10 Years": 6,
        "10+ Years": 10
      };

      const ensureNumberArray = (arr: (string | number)[]): number[] => {
        if (!Array.isArray(arr)) return [];
        return [...new Set(arr.map(item => Number(item)).filter(num => !isNaN(num)))];
      };

      const payload = {
        currentStage: 3,
        currentStep: 1,
        driver: {
          totalVerifiableRelevantExperience: experienceMapping[values.nemtDrivingExperience] || 0,
          totalExperienceYears: experienceMapping[values.totalDrivingExperience] || 0,
          nemtVehicleType: ensureNumberArray(values.vehicleTypes),
          passengerTransported: ensureNumberArray(values.passengerTypes),
          assistanceSkills: ensureNumberArray(values.assistanceSkills),
          tripTypes: ensureNumberArray(values.tripTypes),
          operationalExperience: null, // This field is not in the form but required by API
          transmissionTypes: ensureNumberArray(values.transmissionTypes),
          additionalSkills: ensureNumberArray(values.additionalSkills)
        }
      };

      const response = await submitDriverDetails(payload);

      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Experience saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save experience data";
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting experience:", error);
      toast.error("Failed to save experience. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isDataLoading) {
    return (
      <div style={{ padding: "2rem", textAlign: "center" }}>
        <div style={{ fontSize: "18px", color: "#666" }}>Loading form options...</div>
      </div>
    );
  }

  return (
    <div className={css.nemtDriverExperience}>
      <h3>Step 1: Experience (NEMT Driver)</h3>
      <h5>Detail your experience providing Non-Emergency Medical Transportation, including passenger assistance and vehicle types.</h5>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>

      <form onSubmit={formik.handleSubmit}>
        {/* NEMT Driving Experience */}
        <div className={css.card}>
          <h3>NEMT Driving Experience</h3>
          <div className={css.formRow}>
            <div className={css.col02}>
              <label htmlFor="nemtDrivingExperience">Total Years of Verifiable NEMT Driving Experience:<sup>*</sup></label>
              <Dropdown
                options={experienceYears.map(year => ({ value: year, label: year }))}
                value={formik.values.nemtDrivingExperience}
                placeholder="Select Years"
                onChange={(value) => formik.setFieldValue("nemtDrivingExperience", value)}
                error={formik.touched.nemtDrivingExperience && formik.errors.nemtDrivingExperience ? formik.errors.nemtDrivingExperience : undefined}
                name="nemtDrivingExperience"
              />
            </div>
            <div className={css.col02}>
              <label htmlFor="totalDrivingExperience">Total Years of Driving Experience (Any Type): <span>(If different)</span></label>
              <Dropdown
                options={experienceYears.map(year => ({ value: year, label: year }))}
                value={formik.values.totalDrivingExperience}
                placeholder="Select Years"
                onChange={(value) => formik.setFieldValue("totalDrivingExperience", value)}
                name="totalDrivingExperience"
              />
            </div>
          </div>
        </div>
        {/* Vehicle Types */}
        <div className={css.card}>
          <h3>Type(s) of NEMT Vehicles Driven <sup>*</sup></h3>
          <label>(Check all that apply)</label>

          {vehicleTypeOptions.length > 0 ? (
            <CheckboxGroup
              name="vehicleTypes"
              options={vehicleTypeOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.vehicleTypes}
              onChange={(selected) => {
                formik.setFieldValue('vehicleTypes', selected);
              }}
              otherLabel="Other (Specify Below)"
              otherInputLabel="Other NEMT Vehicle Specified:"
            />
          ) : (
            <div>Loading vehicle options...</div>
          )}
          
          {formik.touched.vehicleTypes && formik.errors.vehicleTypes && (
            <span className={css.error}>
              {formik.errors.vehicleTypes}
            </span>
          )}
        </div>

        {/* Passenger Assistance & Service Experience */}
        <div className={css.card}>
          <h3>Passenger Assistance & Service Experience <sup>*</sup></h3>
          <label>(Check all that apply)</label>

          <h5>Types of Passengers Transported:</h5>
          {passengerTypeOptions.length > 0 ? (
            <CheckboxGroup
              name="passengerTypes"
              options={passengerTypeOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.passengerTypes}
              onChange={(selected) => {
                formik.setFieldValue('passengerTypes', selected);
              }}
            />
          ) : (
            <div>Loading passenger type options...</div>
          )}
          {formik.touched.passengerTypes && formik.errors.passengerTypes && (
            <span className={css.error}>
              {formik.errors.passengerTypes}
            </span>
          )}

          <h5>Assistance Skills:</h5>
          {assistanceSkillOptions.length > 0 ? (
            <CheckboxGroup
              name="assistanceSkills"
              options={assistanceSkillOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.assistanceSkills}
              onChange={(selected) => {
                formik.setFieldValue('assistanceSkills', selected);
              }}
            />
          ) : (
            <div>Loading assistance skill options...</div>
          )}
          {formik.touched.assistanceSkills && formik.errors.assistanceSkills && (
            <span className={css.error}>
              {formik.errors.assistanceSkills}
            </span>
          )}

          <h5>Trip Types / Destinations:</h5>
          {tripTypeOptions.length > 0 ? (
            <CheckboxGroup
              name="tripTypes"
              options={tripTypeOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.tripTypes}
              onChange={(selected) => {
                formik.setFieldValue('tripTypes', selected);
              }}
            />
          ) : (
            <div>Loading trip type options...</div>
          )}
          {formik.touched.tripTypes && formik.errors.tripTypes && (
            <span className={css.error}>
              {formik.errors.tripTypes}
            </span>
          )}
        </div>


        {/* Transmission Type Experience */}
        <div className={css.card}>
          <h3>Transmission Type Experience <sup>*</sup></h3>
          <label>(In NEMT Vehicles Driven) (Check all that apply)</label>

          {transmissionOptions.length > 0 ? (
            <CheckboxGroup
              name="transmissionTypes"
              options={transmissionOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.transmissionTypes}
              onChange={(selected) => {
                formik.setFieldValue('transmissionTypes', selected);
              }}
            />
          ) : (
            <div>Loading transmission options...</div>
          )}
          {formik.touched.transmissionTypes && formik.errors.transmissionTypes && (
            <span className={css.error}>
              {formik.errors.transmissionTypes}
            </span>
          )}
        </div>

        {/* Additional Skills & Experience */}
        <div className={css.card}>
          <h3>Additional Skills & Experience</h3>
          <label>(Optional)</label>

          {additionalSkillsOptions.length > 0 ? (
            <CheckboxGroup
              name="additionalSkills"
              options={additionalSkillsOptions.map(option => ({
                id: option.formValueId,
                label: option.label.en
              }))}
              selectedValues={formik.values.additionalSkills}
              onChange={(selected) => {
                formik.setFieldValue('additionalSkills', selected);
              }}
            />
          ) : (
            <div>Loading additional skills options...</div>
          )}
        </div>

        {/* Navigation Buttons */}
        <div className={css.btnGroup}>
          <button
            type="button"
            onClick={() => router.back()}
            disabled={isLoading}
            className={css.back}
          >
            <img src="/images/icons/arrow_back.svg" alt="Back" /> Back
          </button>

          <button
            type="submit"
            disabled={isLoading}
            className={css.continue}
          >
            {isLoading ? "Saving..." : "Save & Continue"}
          </button>

          <button
            type="button"
            onClick={() => handleSubmit(formik.values, false)}
            disabled={isLoading}
            className={css.exit}
          >
            Save & Exit
          </button>
        </div>
      </form>
    </div>
  );
};

export default Experience;