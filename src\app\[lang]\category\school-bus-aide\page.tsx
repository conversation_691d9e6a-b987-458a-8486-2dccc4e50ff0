// import Consent from '@/components/CategoryForms/Driver/Consents'
// import ConsentReviews from '@/components/CategoryForms/SchoolBusAide/Consents'
// import Documents from '@/components/CategoryForms/SchoolBusAide/Documents'
// import AideExperience from '@/components/CategoryForms/SchoolBusAide/Experience'
// import SkillsCertificate from '@/components/CategoryForms/SchoolBusAide/skills'
// import WorkHistory from '@/components/CategoryForms/SchoolBusAide/WorkHistory'
// import React from 'react'

// function page() {
//   return (
//     <div>
//       {/* <AideExperience/> */}
//     {/* <WorkHistory/> */}
//     <SkillsCertificate/>
//   {/* <ConsentReviews/> */}
//   {/* <Documents/> */}
//     </div>
//   )
// }

// export default page


import SchoolBusAideCategoryWrapper from "@/components/CategoryForms/SchoolBusAide/SchoolBusAideCategoryWrapper";
import React from "react";
 

const Page = () => {
  return (
<>
      <SchoolBusAideCategoryWrapper />
    </>
  );
};

export default Page;

