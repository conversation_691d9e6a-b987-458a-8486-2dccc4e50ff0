"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  fetchSchoolBusAideFormFields,
  fetchDotMedicalCard,
} from "@/services/driverFormService";
import { getStates, State } from "@/services/locationService";
import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtMedical.module.scss';

interface MedicalFormValues {
  dotMedicalRequired: string;
  dotMedicalStatus: string;
  dotMedicalExpiration: Date | null;
  medicalVarianceType: string;
  cprCertified: string;
  cprType: string;
  cprExpiration: Date | null;
  firstAidCertified: string;
  firstAidType: string;
  firstAidExpiration: Date | null;
  patsCertified: string;
  patsDate: Date | null;
  patsAgency: string;
  mavtCertified: string;
  mavtDate: Date | null;
  mavtAgency: string;
  defensiveDriving: string;
  stateNemtCert: string;
  stateNemtState: string;
  stateNemtName: string;
  stateNemtExpiration: Date | null;
}

const NemtMedical: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [states, setStates] = useState<State[]>([]);
  const [dotMedicalStatusOptions, setDotMedicalStatusOptions] = useState<FormValue[]>([]);
  const [cprTypeOptions, setCprTypeOptions] = useState<FormValue[]>([]);
  const [firstAidTypeOptions, setFirstAidTypeOptions] = useState<FormValue[]>([]);

  const validationSchema = Yup.object({
    dotMedicalRequired: Yup.string().required("DOT Medical requirement is required"),
    dotMedicalStatus: Yup.string().when("dotMedicalRequired", {
      is: (val: string) => val === "yes" || val === "unsure",
      then: (schema) => schema.required("DOT Medical status is required"),
      otherwise: (schema) => schema,
    }),
    dotMedicalExpiration: Yup.date().when("dotMedicalStatus", {
      is: (val: string) => val && val !== "disqualified" && val !== "n/a",
      then: (schema) => schema.required("DOT Medical expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    medicalVarianceType: Yup.string().when("dotMedicalStatus", {
      is: "variance",
      then: (schema) => schema.required("Medical variance type is required"),
      otherwise: (schema) => schema,
    }),
    cprCertified: Yup.string().required("CPR certification status is required"),
    cprType: Yup.string().when("cprCertified", {
      is: "yes",
      then: (schema) => schema.required("CPR certification type is required"),
      otherwise: (schema) => schema,
    }),
    cprExpiration: Yup.date().when("cprCertified", {
      is: "yes",
      then: (schema) => schema.required("CPR expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    firstAidCertified: Yup.string().required("First Aid certification status is required"),
    firstAidType: Yup.string().when("firstAidCertified", {
      is: "yes",
      then: (schema) => schema.required("First Aid certification type is required"),
      otherwise: (schema) => schema,
    }),
    firstAidExpiration: Yup.date().when("firstAidCertified", {
      is: "yes",
      then: (schema) => schema.required("First Aid expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    patsCertified: Yup.string().required("PATS certification status is required"),
    mavtCertified: Yup.string().required("MAVT/MAVO certification status is required"),
    defensiveDriving: Yup.string().required("Defensive driving status is required"),
    stateNemtCert: Yup.string().required("State NEMT certification status is required"),
    stateNemtState: Yup.string().when("stateNemtCert", {
      is: "yes",
      then: (schema) => schema.required("State is required"),
      otherwise: (schema) => schema,
    }),
    stateNemtName: Yup.string().when("stateNemtCert", {
      is: "yes",
      then: (schema) => schema.required("Certification name is required"),
      otherwise: (schema) => schema,
    }),
    stateNemtExpiration: Yup.date().when("stateNemtCert", {
      is: "yes",
      then: (schema) => schema.required("Expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
  });

  const formik = useFormik<MedicalFormValues>({
    initialValues: {
      dotMedicalRequired: "No (Driving standard van/car)",
      dotMedicalStatus: "",
      dotMedicalExpiration: null,
      medicalVarianceType: "",
      cprCertified: "Yes",
      cprType: "",
      cprExpiration: null,
      firstAidCertified: "Yes",
      firstAidType: "",
      firstAidExpiration: null,
      patsCertified: "No / Not Formally Certified",
      patsDate: null,
      patsAgency: "",
      mavtCertified: "No / Not Formally Certified",
      mavtDate: null,
      mavtAgency: "",
      defensiveDriving: "No",
      stateNemtCert: "no",
      stateNemtState: "",
      stateNemtName: "",
      stateNemtExpiration: null,
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load states
        const statesData = await getStates();
        setStates(statesData);

        // Load form field options using mixed approach
        try {
          // Use the proven fetchDotMedicalCard for DOT Medical options
          const dotMedicalOptions = await fetchDotMedicalCard();
          console.log("DOT Medical options:", dotMedicalOptions);
          setDotMedicalStatusOptions(dotMedicalOptions);

          // Use School Bus Aide approach for CPR and First Aid options
          const formFieldData = await fetchSchoolBusAideFormFields();
          console.log("Form field data:", formFieldData);

          // Set CPR options
          if (formFieldData["cpr-certification-type-driver-bus-aide-assistant"]) {
            console.log("CPR options:", formFieldData["cpr-certification-type-driver-bus-aide-assistant"]);
            setCprTypeOptions(formFieldData["cpr-certification-type-driver-bus-aide-assistant"]);
          }

          // Set First Aid options
          if (formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]) {
            console.log("First Aid options:", formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]);
            setFirstAidTypeOptions(formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]);
          }
        } catch (error) {
          console.error("Failed to fetch form field options:", error);
        }

        // Load existing driver data
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver;

          // Check if nemtMedical data exists (it might be stored in a different field)
          const medical = (driver as any).nemtMedical;
          if (medical) {
            formik.setValues({
              dotMedicalRequired: medical.dotMedicalRequired || "No (Driving standard van/car)",
              dotMedicalStatus: medical.dotMedicalStatus || "",
              dotMedicalExpiration: medical.dotMedicalExpiration ? new Date(medical.dotMedicalExpiration) : null,
              medicalVarianceType: medical.medicalVarianceType || "",
              cprCertified: medical.cprCertified || "Yes",
              cprType: medical.cprType || "",
              cprExpiration: medical.cprExpiration ? new Date(medical.cprExpiration) : null,
              firstAidCertified: medical.firstAidCertified || "Yes",
              firstAidType: medical.firstAidType || "",
              firstAidExpiration: medical.firstAidExpiration ? new Date(medical.firstAidExpiration) : null,
              patsCertified: medical.patsCertified || "No / Not Formally Certified",
              patsDate: medical.patsDate ? new Date(medical.patsDate) : null,
              patsAgency: medical.patsAgency || "",
              mavtCertified: medical.mavtCertified || "No / Not Formally Certified",
              mavtDate: medical.mavtDate ? new Date(medical.mavtDate) : null,
              mavtAgency: medical.mavtAgency || "",
              defensiveDriving: medical.defensiveDriving || "No",
              stateNemtCert: medical.stateNemtCert || "no",
              stateNemtState: medical.stateNemtState || "",
              stateNemtName: medical.stateNemtName || "",
              stateNemtExpiration: medical.stateNemtExpiration ? new Date(medical.stateNemtExpiration) : null,
            });
          }
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load data. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleSubmit = async (values: MedicalFormValues, shouldContinue: boolean) => {
    try {
      setIsLoading(true);

      const payload = {
        nemtMedical: {
          dotMedicalRequired: values.dotMedicalRequired,
          dotMedicalStatus: values.dotMedicalStatus,
          dotMedicalExpiration: values.dotMedicalExpiration?.toISOString(),
          medicalVarianceType: values.medicalVarianceType,
          cprCertified: values.cprCertified,
          cprType: values.cprType,
          cprExpiration: values.cprExpiration?.toISOString(),
          firstAidCertified: values.firstAidCertified,
          firstAidType: values.firstAidType,
          firstAidExpiration: values.firstAidExpiration?.toISOString(),
          patsCertified: values.patsCertified,
          patsDate: values.patsDate?.toISOString(),
          patsAgency: values.patsAgency,
          mavtCertified: values.mavtCertified,
          mavtDate: values.mavtDate?.toISOString(),
          mavtAgency: values.mavtAgency,
          defensiveDriving: values.defensiveDriving,
          stateNemtCert: values.stateNemtCert,
          stateNemtState: values.stateNemtState,
          stateNemtName: values.stateNemtName,
          stateNemtExpiration: values.stateNemtExpiration?.toISOString(),
        },
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Medical information saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save medical information. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting medical information:", error);
      toast.error("Failed to save medical information. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.medical}>
      <h3>Step 3: Medical & Certifications (NEMT Driver)</h3>
      <p>Provide details about relevant certifications like First Aid, CPR, PATS, MAVT/MAVO, and any state-specific NEMT requirements. DOT Medical Card is typically not required unless driving larger vehicles.</p>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>
      
      <form onSubmit={formik.handleSubmit}>
        {/* DOT Medical Information */}
        <div className={css.section}>
          <h4>DOT Medical Information</h4>
          <p className={css.info}>Generally NOT required for standard NEMT vehicles unless over 10,001 lbs GVWR or designed for 16+ passengers.</p>
          
          <div className={css.formRow}>
            <label>Do your NEMT roles require you to hold a valid DOT Medical Card? *</label>
            <RadioGroup
              name="dotMedicalRequired"
              options={["Yes (Driving larger vehicle / Employer policy)", "No (Driving standard van/car)", "Unsure"]}
              selectedValue={formik.values.dotMedicalRequired}
              onChange={(value) => formik.setFieldValue("dotMedicalRequired", value)}
            />
            {formik.touched.dotMedicalRequired && formik.errors.dotMedicalRequired && (
              <p className={css.error}>{formik.errors.dotMedicalRequired}</p>
            )}
          </div>

          {(formik.values.dotMedicalRequired === "Yes (Driving larger vehicle / Employer policy)" || formik.values.dotMedicalRequired === "Unsure") && (
            <>
              <div className={css.formRow}>
                <label>DOT Medical Card Status: *</label>
                <Dropdown
                  options={dotMedicalStatusOptions.map(option => ({ value: option.label.en, label: option.label.en }))}
                  value={formik.values.dotMedicalStatus}
                  placeholder="Select Status"
                  onChange={(value) => formik.setFieldValue("dotMedicalStatus", value)}
                  error={formik.touched.dotMedicalStatus && formik.errors.dotMedicalStatus ? formik.errors.dotMedicalStatus : undefined}
                  name="dotMedicalStatus"
                />
                {dotMedicalStatusOptions.length === 0 && <p style={{color: 'red', fontSize: '12px'}}>No DOT Medical options loaded</p>}
                {formik.touched.dotMedicalStatus && formik.errors.dotMedicalStatus && (
                  <p className={css.error}>{formik.errors.dotMedicalStatus}</p>
                )}
              </div>

              {formik.values.dotMedicalStatus && formik.values.dotMedicalStatus !== "disqualified" && formik.values.dotMedicalStatus !== "n/a" && (
                <div className={css.formRow}>
                  <label>DOT Medical Card Expiration Date: *</label>
                  <DateInput
                    selected={formik.values.dotMedicalExpiration}
                    onChange={(date) => formik.setFieldValue("dotMedicalExpiration", date)}
                  />
                  {formik.touched.dotMedicalExpiration && formik.errors.dotMedicalExpiration && (
                    <p className={css.error}>{formik.errors.dotMedicalExpiration}</p>
                  )}
                </div>
              )}

              {formik.values.dotMedicalStatus === "variance" && (
                <div className={css.formRow}>
                  <label>Type of Medical Variance/Exemption: *</label>
                  <input
                    type="text"
                    name="medicalVarianceType"
                    value={formik.values.medicalVarianceType}
                    onChange={formik.handleChange}
                    className={css.input}
                  />
                  {formik.touched.medicalVarianceType && formik.errors.medicalVarianceType && (
                    <p className={css.error}>{formik.errors.medicalVarianceType}</p>
                  )}
                </div>
              )}
            </>
          )}
        </div>

        {/* NEMT & Passenger Assistance Certifications */}
        <div className={css.section}>
          <h4>NEMT & Passenger Assistance Certifications</h4>
          
          <div className={css.formRow}>
            <label>Current CPR Certification? * <span className={css.info}>Often required for NEMT. BLS or Heartsaver acceptable.</span></label>
            <RadioGroup
              name="cprCertified"
              options={["Yes", "No / Expired"]}
              selectedValue={formik.values.cprCertified}
              onChange={(value) => formik.setFieldValue("cprCertified", value)}
            />
            {formik.touched.cprCertified && formik.errors.cprCertified && (
              <p className={css.error}>{formik.errors.cprCertified}</p>
            )}
          </div>

          {formik.values.cprCertified === "Yes" && (
            <>
              <div className={css.formRow}>
                <label>CPR Certification Type: *</label>
                <Dropdown
                  options={cprTypeOptions.map(option => ({ value: option.formValueId, label: option.label.en }))}
                  value={formik.values.cprType}
                  placeholder="Select Type"
                  onChange={(value) => formik.setFieldValue("cprType", value)}
                  error={formik.touched.cprType && formik.errors.cprType ? formik.errors.cprType : undefined}
                  name="cprType"
                />
                {cprTypeOptions.length === 0 && <p style={{color: 'red', fontSize: '12px'}}>No CPR options loaded</p>}
              </div>

              <div className={css.formRow}>
                <label>CPR Expiration Date: *</label>
                <DateInput
                  selected={formik.values.cprExpiration}
                  onChange={(date) => formik.setFieldValue("cprExpiration", date)}
                />
                {formik.touched.cprExpiration && formik.errors.cprExpiration && (
                  <p className={css.error}>{formik.errors.cprExpiration}</p>
                )}
              </div>
            </>
          )}

          <div className={css.formRow}>
            <label>Current First Aid Certification? *</label>
            <RadioGroup
              name="firstAidCertified"
              options={["Yes", "No / Expired"]}
              selectedValue={formik.values.firstAidCertified}
              onChange={(value) => formik.setFieldValue("firstAidCertified", value)}
            />
            {formik.touched.firstAidCertified && formik.errors.firstAidCertified && (
              <p className={css.error}>{formik.errors.firstAidCertified}</p>
            )}
          </div>

          {formik.values.firstAidCertified === "Yes" && (
            <>
              <div className={css.formRow}>
                <label>First Aid Certification Type: *</label>
                <Dropdown
                  options={firstAidTypeOptions.map(option => ({ value: option.formValueId, label: option.label.en }))}
                  value={formik.values.firstAidType}
                  placeholder="Select Type"
                  onChange={(value) => formik.setFieldValue("firstAidType", value)}
                  error={formik.touched.firstAidType && formik.errors.firstAidType ? formik.errors.firstAidType : undefined}
                  name="firstAidType"
                />
                {firstAidTypeOptions.length === 0 && <p style={{color: 'red', fontSize: '12px'}}>No First Aid options loaded</p>}
              </div>

              <div className={css.formRow}>
                <label>First Aid Expiration Date: *</label>
                <DateInput
                  selected={formik.values.firstAidExpiration}
                  onChange={(date) => formik.setFieldValue("firstAidExpiration", date)}
                />
                {formik.touched.firstAidExpiration && formik.errors.firstAidExpiration && (
                  <p className={css.error}>{formik.errors.firstAidExpiration}</p>
                )}
              </div>
            </>
          )}

          {/* PATS Certification */}
          <div className={css.formRow}>
            <label>Passenger Assistance Training (PATS / CTAA PASS) Certified? <span className={css.info}>Certifications covering passenger sensitivity, assistance techniques, and wheelchair securement.</span></label>
            <RadioGroup
              name="patsCertified"
              options={["Yes, Current", "Yes, Expired / Need Refresher", "No / Not Formally Certified"]}
              selectedValue={formik.values.patsCertified}
              onChange={(value) => formik.setFieldValue("patsCertified", value)}
            />
            {formik.touched.patsCertified && formik.errors.patsCertified && (
              <p className={css.error}>{formik.errors.patsCertified}</p>
            )}
          </div>

          {formik.values.patsCertified.startsWith("Yes") && (
            <>
              <div className={css.formRow}>
                <label>PATS/PASS Certification Date (Approx): (Optional)</label>
                <DateInput
                  selected={formik.values.patsDate}
                  onChange={(date) => formik.setFieldValue("patsDate", date)}
                />
              </div>

              <div className={css.formRow}>
                <label>Issuing Agency/Trainer: (Optional)</label>
                <input
                  type="text"
                  name="patsAgency"
                  value={formik.values.patsAgency}
                  onChange={formik.handleChange}
                  className={css.input}
                />
              </div>
            </>
          )}

          {/* MAVT/MAVO Certification */}
          <div className={css.formRow}>
            <label>MAVT / MAVO (Mobility Assistance Vehicle Tech/Operator) Certified? <span className={css.info}>Certification often required for operating wheelchair lift vehicles and proper securement.</span></label>
            <RadioGroup
              name="mavtCertified"
              options={["Yes, Current", "Yes, Expired / Need Refresher", "No / Not Formally Certified"]}
              selectedValue={formik.values.mavtCertified}
              onChange={(value) => formik.setFieldValue("mavtCertified", value)}
            />
            {formik.touched.mavtCertified && formik.errors.mavtCertified && (
              <p className={css.error}>{formik.errors.mavtCertified}</p>
            )}
          </div>

          {formik.values.mavtCertified.startsWith("Yes") && (
            <>
              <div className={css.formRow}>
                <label>MAVT/MAVO Certification Date (Approx): (Optional)</label>
                <DateInput
                  selected={formik.values.mavtDate}
                  onChange={(date) => formik.setFieldValue("mavtDate", date)}
                />
              </div>

              <div className={css.formRow}>
                <label>Issuing Agency/Trainer: (Optional)</label>
                <input
                  type="text"
                  name="mavtAgency"
                  value={formik.values.mavtAgency}
                  onChange={formik.handleChange}
                  className={css.input}
                />
              </div>
            </>
          )}

          {/* Defensive Driving */}
          <div className={css.formRow}>
            <label>Defensive Driving Course Completed? (Often required by insurance/employers)</label>
            <RadioGroup
              name="defensiveDriving"
              options={["Yes, within last 3 years", "Yes, longer than 3 years ago", "No"]}
              selectedValue={formik.values.defensiveDriving}
              onChange={(value) => formik.setFieldValue("defensiveDriving", value)}
            />
            {formik.touched.defensiveDriving && formik.errors.defensiveDriving && (
              <p className={css.error}>{formik.errors.defensiveDriving}</p>
            )}
          </div>

          {/* State NEMT Certification */}
          <div className={css.formRow}>
            <label>State-Specific NEMT Driver Certification/Permit Held? (Check local regulations)</label>
            <RadioGroup
              name="stateNemtCert"
              options={["yes", "no", "unsure"]}
              selectedValue={formik.values.stateNemtCert}
              onChange={(value) => formik.setFieldValue("stateNemtCert", value)}
            />
            {formik.touched.stateNemtCert && formik.errors.stateNemtCert && (
              <p className={css.error}>{formik.errors.stateNemtCert}</p>
            )}
          </div>

          {formik.values.stateNemtCert === "yes" && (
            <>
              <div className={css.formRow}>
                <label>State: *</label>
                <Dropdown
                  options={states.map(state => ({ value: state.slug, label: state.name.en }))}
                  value={formik.values.stateNemtState}
                  placeholder="Select State"
                  onChange={(value) => formik.setFieldValue("stateNemtState", value)}
                  error={formik.touched.stateNemtState && formik.errors.stateNemtState ? formik.errors.stateNemtState : undefined}
                  name="stateNemtState"
                />
              </div>

              <div className={css.formRow}>
                <label>Certification/Permit Name: *</label>
                <input
                  type="text"
                  name="stateNemtName"
                  value={formik.values.stateNemtName}
                  onChange={formik.handleChange}
                  className={css.input}
                />
                {formik.touched.stateNemtName && formik.errors.stateNemtName && (
                  <p className={css.error}>{formik.errors.stateNemtName}</p>
                )}
              </div>

              <div className={css.formRow}>
                <label>Expiration Date: *</label>
                <DateInput
                  selected={formik.values.stateNemtExpiration}
                  onChange={(date) => formik.setFieldValue("stateNemtExpiration", date)}
                />
                {formik.touched.stateNemtExpiration && formik.errors.stateNemtExpiration && (
                  <p className={css.error}>{formik.errors.stateNemtExpiration}</p>
                )}
              </div>
            </>
          )}
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 2: History)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              className={css.saveExitBtn}
              disabled={isLoading}
            >
              Save & Exit (Complete Later)
            </button>
            <button
              type="submit"
              className={css.continueBtn}
              disabled={isLoading}
            >
              Save & Continue (To Step 4: Docs) →
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtMedical;
