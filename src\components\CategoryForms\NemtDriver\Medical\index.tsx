"use client";
import React, { useEffect, useState } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useNemtDriverCategory } from "@/contexts/CommonDriverCategoryContext";
import {
  FormValue,
  submitDriverDetails,
  fetchDriverDetails,
  fetchSchoolBusAideFormFields,
  fetchDotMedicalCard,
} from "@/services/driverFormService";

import DateInput from "@/components/Common/DateInput/DateInput";
import RadioGroup from "@/components/Common/RadioGroup";
import Dropdown from "@/components/Common/Dropdown";
import css from './nemtMedical.module.scss';

interface OtherCertification {
  id?: string;
  driverOtherCertificationId?: number;
  certificateName: string;
  issuingBody: string;
  expirationDate: Date | null;
  dateIssued: Date | null;
  rank: number;
}

interface MedicalFormValues {
  dotMedicalCardStatus: string;
  dotExpirationDate: Date | null;
  dotExaminerName: string;
  dotExaminerPhone: string;
  dotNationalRegistryNumber: string;
  dotRestriction: string;
  dotExemption: string;
  cprCertification: string;
  cprCertificationType: string;
  cprCertificationExpirationDate: Date | null;
  firstAidCertification: string;
  firstAidCertificationType: string;
  firstAidCertificationExpirationDate: Date | null;
  patsCertification: string;
  patsCertificationDate: Date | null;
  patsCertificateIssuingAgency: string;
  mavoCertification: string;
  mavoCertificationDate: Date | null;
  mavoCertificateIssuingAgency: string;
  defensiveDrivingCourseCompleted: string;
  holdOtherCertification: string;
  otherCertifications: OtherCertification[];
}

const NemtMedical: React.FC = () => {
  const { updateStepFromApiResponse, goToPreviousStep, canGoBack } = useNemtDriverCategory();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const [dotMedicalStatusOptions, setDotMedicalStatusOptions] = useState<FormValue[]>([]);
  const [cprTypeOptions, setCprTypeOptions] = useState<FormValue[]>([]);
  const [firstAidTypeOptions, setFirstAidTypeOptions] = useState<FormValue[]>([]);

  const validationSchema = Yup.object({
    dotMedicalCardStatus: Yup.string(),
    dotExpirationDate: Yup.date().nullable(),
    dotExaminerName: Yup.string(),
    dotExaminerPhone: Yup.string(),
    dotNationalRegistryNumber: Yup.string(),
    dotRestriction: Yup.string(),
    dotExemption: Yup.string(),
    cprCertification: Yup.string(),
    cprCertificationType: Yup.string().when("cprCertification", {
      is: "yes",
      then: (schema) => schema.required("CPR certification type is required"),
      otherwise: (schema) => schema,
    }),
    cprCertificationExpirationDate: Yup.date().when("cprCertification", {
      is: "yes",
      then: (schema) => schema.required("CPR expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    firstAidCertification: Yup.string(),
    firstAidCertificationType: Yup.string().when("firstAidCertification", {
      is: "yes",
      then: (schema) => schema.required("First Aid certification type is required"),
      otherwise: (schema) => schema,
    }),
    firstAidCertificationExpirationDate: Yup.date().when("firstAidCertification", {
      is: "yes",
      then: (schema) => schema.required("First Aid expiration date is required"),
      otherwise: (schema) => schema.nullable(),
    }),
    patsCertification: Yup.string(),
    patsCertificationDate: Yup.date().nullable(),
    patsCertificateIssuingAgency: Yup.string(),
    mavoCertification: Yup.string(),
    mavoCertificationDate: Yup.date().nullable(),
    mavoCertificateIssuingAgency: Yup.string(),
    defensiveDrivingCourseCompleted: Yup.string(),
    holdOtherCertification: Yup.string(),
    otherCertifications: Yup.array().of(
      Yup.object({
        certificateName: Yup.string().required("Certificate name is required"),
        issuingBody: Yup.string().required("Issuing body is required"),
        expirationDate: Yup.date().nullable(),
        dateIssued: Yup.date().nullable(),
      })
    ),
  });

  const formik = useFormik<MedicalFormValues>({
    initialValues: {
      dotMedicalCardStatus: "",
      dotExpirationDate: null,
      dotExaminerName: "",
      dotExaminerPhone: "",
      dotNationalRegistryNumber: "",
      dotRestriction: "",
      dotExemption: "",
      cprCertification: "no",
      cprCertificationType: "",
      cprCertificationExpirationDate: null,
      firstAidCertification: "no",
      firstAidCertificationType: "",
      firstAidCertificationExpirationDate: null,
      patsCertification: "no",
      patsCertificationDate: null,
      patsCertificateIssuingAgency: "",
      mavoCertification: "no",
      mavoCertificationDate: null,
      mavoCertificateIssuingAgency: "",
      defensiveDrivingCourseCompleted: "no",
      holdOtherCertification: "no",
      otherCertifications: [],
    },
    validationSchema,
    onSubmit: async (values) => {
      await handleSubmit(values, true);
    },
  });

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);

        // Load form field options using mixed approach
        try {
          // Use the proven fetchDotMedicalCard for DOT Medical options
          const dotMedicalOptions = await fetchDotMedicalCard();
          console.log("DOT Medical options:", dotMedicalOptions);
          setDotMedicalStatusOptions(dotMedicalOptions);

          // Use School Bus Aide approach for CPR and First Aid options
          const formFieldData = await fetchSchoolBusAideFormFields();
          console.log("Form field data:", formFieldData);

          // Set CPR options
          if (formFieldData["cpr-certification-type-driver-bus-aide-assistant"]) {
            console.log("CPR options:", formFieldData["cpr-certification-type-driver-bus-aide-assistant"]);
            setCprTypeOptions(formFieldData["cpr-certification-type-driver-bus-aide-assistant"]);
          }

          // Set First Aid options
          if (formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]) {
            console.log("First Aid options:", formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]);
            setFirstAidTypeOptions(formFieldData["first-aid-certification-type-driver-bus-aide-assistant"]);
          }
        } catch (error) {
          console.error("Failed to fetch form field options:", error);
        }

        // Load existing driver data
        const response = await fetchDriverDetails();
        if (response?.status && response?.data?.driver) {
          const driver = response.data.driver as any;

          // Map API response to form values
          const mappedOtherCertifications = driver.driverOtherCertifications?.map((cert: any, index: number) => ({
            id: cert.driverOtherCertificationId?.toString() || `cert-${index}`,
            driverOtherCertificationId: cert.driverOtherCertificationId,
            certificateName: cert.certificateName || "",
            issuingBody: cert.issuingBody || "",
            expirationDate: cert.expirationDate ? new Date(cert.expirationDate) : null,
            dateIssued: cert.dateIssued ? new Date(cert.dateIssued) : null,
            rank: cert.rank || index + 1,
          })) || [];

          formik.setValues({
            dotMedicalCardStatus: driver.dotMedicalCardStatus?.toString() || "",
            dotExpirationDate: driver.dotExpirationDate ? new Date(driver.dotExpirationDate) : null,
            dotExaminerName: driver.dotExaminerName || "",
            dotExaminerPhone: driver.dotExaminerPhone || "",
            dotNationalRegistryNumber: driver.dotNationalRegistryNumber || "",
            dotRestriction: driver.dotRestriction || "",
            dotExemption: driver.dotExemption || "",
            cprCertification: driver.cprCertification ? "yes" : "no",
            cprCertificationType: driver.cprCertificationType?.toString() || "",
            cprCertificationExpirationDate: driver.cprCertificationExpirationDate ? new Date(driver.cprCertificationExpirationDate) : null,
            firstAidCertification: driver.firstAidCertification ? "yes" : "no",
            firstAidCertificationType: driver.firstAidCertificationType?.toString() || "",
            firstAidCertificationExpirationDate: driver.firstAidCertificationExpirationDate ? new Date(driver.firstAidCertificationExpirationDate) : null,
            patsCertification: driver.patsCertification ? "yes" : "no",
            patsCertificationDate: driver.patsCertificationDate ? new Date(driver.patsCertificationDate) : null,
            patsCertificateIssuingAgency: driver.patsCertificateIssuingAgency || "",
            mavoCertification: driver.mavoCertification ? "yes" : "no",
            mavoCertificationDate: driver.mavoCertificationDate ? new Date(driver.mavoCertificationDate) : null,
            mavoCertificateIssuingAgency: driver.mavoCertificateIssuingAgency || "",
            defensiveDrivingCourseCompleted: driver.defensiveDrivingCourseCompleted ? "yes" : "no",
            holdOtherCertification: driver.holdOtherCertification ? "yes" : "no",
            otherCertifications: mappedOtherCertifications,
          });
        }
      } catch (error) {
        console.error("Error loading data:", error);
        toast.error("Failed to load data. Please refresh.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const addOtherCertification = () => {
    const newCert: OtherCertification = {
      id: `cert-${Date.now()}`,
      certificateName: "",
      issuingBody: "",
      expirationDate: null,
      dateIssued: null,
      rank: formik.values.otherCertifications.length + 1,
    };
    formik.setFieldValue("otherCertifications", [...formik.values.otherCertifications, newCert]);
  };

  const removeOtherCertification = (index: number) => {
    const updatedCerts = formik.values.otherCertifications.filter((_, i) => i !== index);
    formik.setFieldValue("otherCertifications", updatedCerts);
  };

  const handleSubmit = async (values: MedicalFormValues, shouldContinue: boolean) => {
    try {
      setIsLoading(true);

      const payload = {
        currentStage: 3,
        currentStep: 3,
        driver: {
          dotMedicalCardStatus: values.dotMedicalCardStatus ? parseInt(values.dotMedicalCardStatus) : null,
          dotExpirationDate: values.dotExpirationDate ? values.dotExpirationDate.toISOString() : null,
          dotExaminerName: values.dotExaminerName || null,
          dotExaminerPhone: values.dotExaminerPhone || null,
          dotNationalRegistryNumber: values.dotNationalRegistryNumber || null,
          dotRestriction: values.dotRestriction || null,
          dotExemption: values.dotExemption || null,
          cprCertification: values.cprCertification === "yes",
          cprCertificationType: values.cprCertificationType ? parseInt(values.cprCertificationType) : null,
          cprCertificationExpirationDate: values.cprCertificationExpirationDate ? values.cprCertificationExpirationDate.toISOString() : null,
          firstAidCertification: values.firstAidCertification === "yes",
          firstAidCertificationType: values.firstAidCertificationType ? parseInt(values.firstAidCertificationType) : null,
          firstAidCertificationExpirationDate: values.firstAidCertificationExpirationDate ? values.firstAidCertificationExpirationDate.toISOString() : null,
          patsCertification: values.patsCertification === "yes",
          patsCertificationDate: values.patsCertificationDate ? values.patsCertificationDate.toISOString() : null,
          patsCertificateIssuingAgency: values.patsCertificateIssuingAgency || null,
          mavoCertification: values.mavoCertification === "yes",
          mavoCertificationDate: values.mavoCertificationDate ? values.mavoCertificationDate.toISOString() : null,
          mavoCertificateIssuingAgency: values.mavoCertificateIssuingAgency || null,
          defensiveDrivingCourseCompleted: values.defensiveDrivingCourseCompleted === "yes",
          holdOtherCertification: values.holdOtherCertification === "yes",
          driverOtherCertifications: values.otherCertifications.map((cert, index) => ({
            ...(cert.driverOtherCertificationId && { driverOtherCertificationId: cert.driverOtherCertificationId }),
            certificateName: cert.certificateName,
            issuingBody: cert.issuingBody,
            expirationDate: cert.expirationDate ? cert.expirationDate.toISOString() : null,
            dateIssued: cert.dateIssued ? cert.dateIssued.toISOString() : null,
            rank: index + 1,
          })),
        },
      };

      const response = await submitDriverDetails(payload);
      if (response && response.status) {
        if (shouldContinue) {
          updateStepFromApiResponse(response);
          toast.success("Medical information saved successfully!");
          window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
          toast.success("Draft saved successfully! Redirecting to home...");
          setTimeout(() => {
            router.push("/");
          }, 1500);
        }
      } else {
        const errorMessage = response?.message || response?.error?.message || "Failed to save medical information. Please try again.";
        toast.error(errorMessage);
      }
    } catch (error) {
      console.error("Error submitting medical information:", error);
      toast.error("Failed to save medical information. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return <div className={css.loading}>Loading...</div>;
  }

  return (
    <div className={css.medical}>
      <h3>Step 3: Medical & Certifications (NEMT Driver)</h3>
      <p>Provide details about relevant certifications like First Aid, CPR, PATS, MAVT/MAVO, and DOT Medical information if applicable.</p>
      <h6 className={css.required}>Required fields are marked with <sup>*</sup></h6>

      <form onSubmit={formik.handleSubmit}>
        {/* DOT Medical Information */}
        <div className={css.section}>
          <h4>DOT Medical Information</h4>
          <p className={css.info}>Complete if you have or need a DOT Medical Card for your NEMT role.</p>

          <div className={css.formRow}>
            <label>DOT Medical Card Status:</label>
            <Dropdown
              options={dotMedicalStatusOptions.map(option => ({ value: option.formValueId.toString(), label: option.label.en }))}
              value={formik.values.dotMedicalCardStatus}
              placeholder="Select Status"
              onChange={(value) => formik.setFieldValue("dotMedicalCardStatus", value)}
              error={formik.touched.dotMedicalCardStatus && formik.errors.dotMedicalCardStatus ? formik.errors.dotMedicalCardStatus : undefined}
              name="dotMedicalCardStatus"
            />
          </div>

          {formik.values.dotMedicalCardStatus && (
            <>
              <div className={css.formRow}>
                <label>DOT Medical Card Expiration Date:</label>
                <DateInput
                  selected={formik.values.dotExpirationDate}
                  onChange={(date) => formik.setFieldValue("dotExpirationDate", date)}
                />
              </div>

              <div className={css.formRow}>
                <label>DOT Examiner Name:</label>
                <input
                  type="text"
                  name="dotExaminerName"
                  value={formik.values.dotExaminerName}
                  onChange={formik.handleChange}
                  className={css.input}
                  placeholder="Dr. Jane Smith"
                />
              </div>

              <div className={css.formRow}>
                <label>DOT Examiner Phone:</label>
                <input
                  type="tel"
                  name="dotExaminerPhone"
                  value={formik.values.dotExaminerPhone}
                  onChange={formik.handleChange}
                  className={css.input}
                  placeholder="(*************"
                />
              </div>

              <div className={css.formRow}>
                <label>DOT National Registry Number:</label>
                <input
                  type="text"
                  name="dotNationalRegistryNumber"
                  value={formik.values.dotNationalRegistryNumber}
                  onChange={formik.handleChange}
                  className={css.input}
                  placeholder="1234567890"
                />
              </div>

              <div className={css.formRow}>
                <label>DOT Restriction (if any):</label>
                <input
                  type="text"
                  name="dotRestriction"
                  value={formik.values.dotRestriction}
                  onChange={formik.handleChange}
                  className={css.input}
                  placeholder="Corrective Lenses Required"
                />
              </div>

              <div className={css.formRow}>
                <label>DOT Exemption (if any):</label>
                <input
                  type="text"
                  name="dotExemption"
                  value={formik.values.dotExemption}
                  onChange={formik.handleChange}
                  className={css.input}
                  placeholder="FMCSA Vision Exemption"
                />
              </div>
            </>
          )}
        </div>

        {/* NEMT & Passenger Assistance Certifications */}
        <div className={css.section}>
          <h4>NEMT & Passenger Assistance Certifications</h4>

          <div className={css.formRow}>
            <label>Current CPR Certification? <span className={css.info}>Often required for NEMT. BLS or Heartsaver acceptable.</span></label>
            <RadioGroup
              name="cprCertification"
              options={["yes", "no"]}
              selectedValue={formik.values.cprCertification}
              onChange={(value) => formik.setFieldValue("cprCertification", value)}
            />
          </div>

          {formik.values.cprCertification === "yes" && (
            <>
              <div className={css.formRow}>
                <label>CPR Certification Type: *</label>
                <Dropdown
                  options={cprTypeOptions.map(option => ({ value: option.formValueId.toString(), label: option.label.en }))}
                  value={formik.values.cprCertificationType}
                  placeholder="Select Type"
                  onChange={(value) => formik.setFieldValue("cprCertificationType", value)}
                  error={formik.touched.cprCertificationType && formik.errors.cprCertificationType ? formik.errors.cprCertificationType : undefined}
                  name="cprCertificationType"
                />
              </div>

              <div className={css.formRow}>
                <label>CPR Expiration Date: *</label>
                <DateInput
                  selected={formik.values.cprCertificationExpirationDate}
                  onChange={(date) => formik.setFieldValue("cprCertificationExpirationDate", date)}
                />
                {formik.touched.cprCertificationExpirationDate && formik.errors.cprCertificationExpirationDate && (
                  <p className={css.error}>{formik.errors.cprCertificationExpirationDate}</p>
                )}
              </div>
            </>
          )}

          <div className={css.formRow}>
            <label>Current First Aid Certification?</label>
            <RadioGroup
              name="firstAidCertification"
              options={["yes", "no"]}
              selectedValue={formik.values.firstAidCertification}
              onChange={(value) => formik.setFieldValue("firstAidCertification", value)}
            />
          </div>

          {formik.values.firstAidCertification === "yes" && (
            <>
              <div className={css.formRow}>
                <label>First Aid Certification Type: *</label>
                <Dropdown
                  options={firstAidTypeOptions.map(option => ({ value: option.formValueId.toString(), label: option.label.en }))}
                  value={formik.values.firstAidCertificationType}
                  placeholder="Select Type"
                  onChange={(value) => formik.setFieldValue("firstAidCertificationType", value)}
                  error={formik.touched.firstAidCertificationType && formik.errors.firstAidCertificationType ? formik.errors.firstAidCertificationType : undefined}
                  name="firstAidCertificationType"
                />
              </div>

              <div className={css.formRow}>
                <label>First Aid Expiration Date: *</label>
                <DateInput
                  selected={formik.values.firstAidCertificationExpirationDate}
                  onChange={(date) => formik.setFieldValue("firstAidCertificationExpirationDate", date)}
                />
                {formik.touched.firstAidCertificationExpirationDate && formik.errors.firstAidCertificationExpirationDate && (
                  <p className={css.error}>{formik.errors.firstAidCertificationExpirationDate}</p>
                )}
              </div>
            </>
          )}

          {/* PATS Certification */}
          <div className={css.formRow}>
            <label>Passenger Assistance Training (PATS / CTAA PASS) Certified? <span className={css.info}>Certifications covering passenger sensitivity, assistance techniques, and wheelchair securement.</span></label>
            <RadioGroup
              name="patsCertification"
              options={["yes", "no"]}
              selectedValue={formik.values.patsCertification}
              onChange={(value) => formik.setFieldValue("patsCertification", value)}
            />
          </div>

          {formik.values.patsCertification === "yes" && (
            <>
              <div className={css.formRow}>
                <label>PATS/PASS Certification Date:</label>
                <DateInput
                  selected={formik.values.patsCertificationDate}
                  onChange={(date) => formik.setFieldValue("patsCertificationDate", date)}
                />
              </div>

              <div className={css.formRow}>
                <label>Issuing Agency/Trainer:</label>
                <input
                  type="text"
                  name="patsCertificateIssuingAgency"
                  value={formik.values.patsCertificateIssuingAgency}
                  onChange={formik.handleChange}
                  className={css.input}
                  placeholder="HealthTrust Institute"
                />
              </div>
            </>
          )}

          {/* MAVO Certification */}
          <div className={css.formRow}>
            <label>MAVO (Mobility Assistance Vehicle Operator) Certified? <span className={css.info}>Certification often required for operating wheelchair lift vehicles and proper securement.</span></label>
            <RadioGroup
              name="mavoCertification"
              options={["yes", "no"]}
              selectedValue={formik.values.mavoCertification}
              onChange={(value) => formik.setFieldValue("mavoCertification", value)}
            />
          </div>

          {formik.values.mavoCertification === "yes" && (
            <>
              <div className={css.formRow}>
                <label>MAVO Certification Date:</label>
                <DateInput
                  selected={formik.values.mavoCertificationDate}
                  onChange={(date) => formik.setFieldValue("mavoCertificationDate", date)}
                />
              </div>

              <div className={css.formRow}>
                <label>Issuing Agency/Trainer:</label>
                <input
                  type="text"
                  name="mavoCertificateIssuingAgency"
                  value={formik.values.mavoCertificateIssuingAgency}
                  onChange={formik.handleChange}
                  className={css.input}
                  placeholder="National Safety Council"
                />
              </div>
            </>
          )}

          {/* Defensive Driving */}
          <div className={css.formRow}>
            <label>Defensive Driving Course Completed? (Often required by insurance/employers)</label>
            <RadioGroup
              name="defensiveDrivingCourseCompleted"
              options={["yes", "no"]}
              selectedValue={formik.values.defensiveDrivingCourseCompleted}
              onChange={(value) => formik.setFieldValue("defensiveDrivingCourseCompleted", value)}
            />
          </div>

          {/* Other Certifications */}
          <div className={css.formRow}>
            <label>Do you hold any other relevant certifications?</label>
            <RadioGroup
              name="holdOtherCertification"
              options={["yes", "no"]}
              selectedValue={formik.values.holdOtherCertification}
              onChange={(value) => formik.setFieldValue("holdOtherCertification", value)}
            />
          </div>

          {formik.values.holdOtherCertification === "yes" && (
            <div className={css.otherCertifications}>
              <div className={css.certificationHeader}>
                <h5>Other Certifications</h5>
                <button
                  type="button"
                  onClick={addOtherCertification}
                  className={css.addBtn}
                >
                  + Add Certification
                </button>
              </div>

              {formik.values.otherCertifications.map((cert, index) => (
                <div key={cert.id} className={css.certificationBlock}>
                  <h6>Certification {index + 1}</h6>

                  <div className={css.formRow}>
                    <label>Certificate Name: *</label>
                    <input
                      type="text"
                      name={`otherCertifications[${index}].certificateName`}
                      value={cert.certificateName}
                      onChange={formik.handleChange}
                      className={css.input}
                      placeholder="HAZMAT Endorsement"
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>Issuing Body: *</label>
                    <input
                      type="text"
                      name={`otherCertifications[${index}].issuingBody`}
                      value={cert.issuingBody}
                      onChange={formik.handleChange}
                      className={css.input}
                      placeholder="FMCSA"
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>Date Issued:</label>
                    <DateInput
                      selected={cert.dateIssued}
                      onChange={(date) => formik.setFieldValue(`otherCertifications[${index}].dateIssued`, date)}
                    />
                  </div>

                  <div className={css.formRow}>
                    <label>Expiration Date:</label>
                    <DateInput
                      selected={cert.expirationDate}
                      onChange={(date) => formik.setFieldValue(`otherCertifications[${index}].expirationDate`, date)}
                    />
                  </div>

                  {formik.values.otherCertifications.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeOtherCertification(index)}
                      className={css.removeBtn}
                    >
                      Remove This Certification
                    </button>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className={css.navigationButtons}>
          {canGoBack && (
            <button
              type="button"
              onClick={goToPreviousStep}
              className={css.backBtn}
            >
              ← Back (To Step 2: History)
            </button>
          )}
          <div className={css.rightButtons}>
            <button
              type="button"
              onClick={() => handleSubmit(formik.values, false)}
              className={css.saveExitBtn}
              disabled={isLoading}
            >
              Save & Exit (Complete Later)
            </button>
            <button
              type="submit"
              className={css.continueBtn}
              disabled={isLoading}
            >
              Save & Continue (To Step 4: Docs) →
            </button>
          </div>
        </div>
      </form>
    </div>
  );
};

export default NemtMedical;
