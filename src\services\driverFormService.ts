// import { error } from "console";
import { getCookie } from "cookies-next";

export interface DriverDetailsPayload {
  currentStage: number;
  currentStep: number;
  driver: {
    firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    phoneNumber?: string | null;
    street?: string | null;
    apartmentNumber?: string | null;
    zipCode?: number | null;
    city?: string | null;
    state?: string | null;
    isLegallyAuthorizedToWorkInUs: boolean;
    futureSponsorshipNeeded: boolean;
  };
}

export interface DriverDocument {
  filename: string;
  filepath: string;
  multimediaId?: number; 
}
export interface FetchDriverDetailsResponse {
  status: boolean;
  message?: string;
  error?: {
    code?: string | null;
    message?: string;
  };
  data: {
      currentStage?: number;
    currentStep?: number;
    driver: {
        firstName?: string | null;
    lastName?: string | null;
    email?: string | null;
    phoneNumber?: string | null;
    street?: string | null;
    apartmentNumber?: string | null;
    zipCode?: number | null;
    city?: string | null;
    state?: string | null;
    isLegallyAuthorizedToWorkInUs: boolean;
    futureSponsorshipNeeded: boolean;
    driverCategory?: number;
      // License information
      driverLicenseClass?: number;
      driverLicenseState?: number;
      driverLicenseNumber?: string | null;
      driverLicenseExpiration?: string;
      driverLicenseEndorsements?: number[];
      driverLicenseRestrictions?: number[];
      // Safety information
      isAccidentLast3Years?: boolean;
      numberOfAccidentsLast3Years?: number;
      briefDescriptionOfAccidentsLast3Years?: string;
      isTrafficViolationLast3Years?: boolean;
      numberOfTrafficViolationsLast3Years?: number;
      briefDescriptionOfTrafficViolationsLast3Years?: string;
      isDriverLicenseSuspended?: boolean;
      briefDescriptionOfSuspension?: string;
      dotMedicalCardStatus: number;
      dotExpirationDate: string | null;
      dotExaminerName: string;
      dotExaminerPhone: string;
      dotNationalRegistryNumber: string;
      dotRestriction: string;
      dotExemption: string;
      dotExaminerState: number;
      holdOtherCertification: boolean;
      holdBusCertification: boolean;
      medicalVarianceDetails: string;
      driverOtherCertifications: {
        certificateName: string;
        issuingBody: string;
        expirationDate: string | null;
        dateIssued: string | null;
      }[];
       driverBusCertifications : {
        certificateName: string;
        issuingBody:string;
        expirationDate: string | null;
        dateIssued: string | null;
      }[];
      totalVerifiableCdlExperience: number;
      totalVerifiableMiles: number;
      trailerTypes: number[];
      straightTruckTypes: number[];
      busTypes: number[];
      otherCdlEquipment: (number | string)[];
      tankerSpecifics: number[];
      transmissionTypes:  string[];
      routeTypes: number[];
      operatingArea: number[];
      // School Bus Driver specific properties
      totalVerifiableBusDriverExperience?: number;
      studentTransportationVehicle?: number[];
      ageGroupTransported?: number[];
      specialNeedTransported?: number[];
      additionalSkills?: number[];
      driverEmploymentHistory: {
        isUnemployment: boolean;
        driverEmploymentHistoryId:number;
        employerName?: string;
        typeOfEmployment?: string|number;

        employerStreet?: string;
        employerCity?: string;
        employerState?: string;
        employerZip?: string;
        employerPhone?: string;
        employerWebsite?: string;
        employerManagerName?: string;
        positionHeld?: string;
        subjectToFmcsa?: boolean;
        operatedCmv?: boolean;
        contactPermission?: boolean;
        startDate: string | null;
        endDate: string | null;
        isCurrent: boolean;
        reasonForLeaving: string;
        explanation: string;
        rank: number;
      }[];
      documents: {
        dot_medical_card: DriverDocument[];
        twic_card: DriverDocument[];
        hazmat_and_others: DriverDocument[];
        medical_variance: DriverDocument[];
        state_certifications: DriverDocument[];
        first_aid_cpr: DriverDocument[];
        other_training: DriverDocument[];
        social_security: DriverDocument[];
        proof_of_work: DriverDocument[];
        resume: DriverDocument[];
      };
      availability: number; // numeric ID from dropdown
      availabilitySpecificDate: string | null; // ISO date string
      employmentType: number[];
      preferredRouteType: number[];
      willingToRelocate: boolean;
      workSplitShift: boolean; // 0 = No, 1 = Yes, 2 = Prefer Not
      employmentArrangement: number;
      consentShareProfile: boolean;
      consentBackgroundCheck: boolean;
      consentClearinghouse: boolean;
      consentCertifyInfo: boolean;
      consentAcceptTerms: boolean;
      preferredSchedule:number[];
      cprCertification:string;
      cprCertificationExpirationDate:Date|null;
      firstAidCertification:string;
      firstAidCertificationExpirationDate:Date|null;
      behaviorTraining:string;
      behaviorTrainingName:string;
      behaviorTrainingCompletedDate:Date|null;
      patsCertificateIssuingAgency:string;
      patsCertification:Date|null;
      childAbuseClearanceHeld:string;
      childAbuseClearance:number
      patsCertificationType:number
    };
  };
}


export interface FormValue {
  formValueId: number;
  formValueSlug: string;
  label: {
    en: string;
    es: string;
  };
  description: {
    en: string;
    es: string;
  };
}

export interface LicenseResponse {
  status: boolean;
  data: {
    formFieldId: number;
    formFieldSlug: string;
    componentType: string;
    formValues: FormValue[];
    label: {
      en: string;
      es: string;
    };
    description: {
      en: string;
      es: string;
    };
  };
  message: string;
}

export async function submitDriverDetails(
  payload: object
): Promise<FetchDriverDetailsResponse | null> {
  const token = getCookie("authToken");

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}driver/driver-details`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `${token}`,
        },
        body: JSON.stringify(payload),
      }
    );

    const data = await response.json();
    if (!response.ok && response.status !== 400) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data as FetchDriverDetailsResponse;
  } catch (error) {
    console.error("API Error:", error);
    return null;
  }
}

export async function fetchDriverDetails(): Promise<FetchDriverDetailsResponse |null> {
  const token = getCookie("authToken");
  if (!token) {
    console.error('No auth token found in service');
    return null;
  }

  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}driver/driver-details`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `${token}`,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("API Error:", error);
    return null;
  }
}

export const getAllCategories = async () => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}/category/get-all`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!res.ok) {
      const errorData = await res.json();
      throw new Error(errorData.message || "Failed to fetch categories");
    }

    const data = await res.json();
    return data;
  } catch (error: unknown) {
    if (error instanceof Error) {
      throw new Error(error.message || "Something went wrong");
    } else {
      throw new Error("An unknown error occurred while fetching categories");
    }
  }
};

export const fetchDriverLicenseClasses = async (): Promise<
  FormValue[] | null
> => {
  try {
    const res = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/highest-drivers-license-class-held-driver`
    );
    if (!res.ok) throw new Error("Failed to fetch license classes");

    const json: LicenseResponse = await res.json();
    return json?.data?.formValues || [];
  } catch (error) {
    console.error("API Error:", error);
    return null;
  }
};

export const fetchCdlEndorsements = async (): Promise<FormValue[]> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/cdl-endorsements-driver`
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch endorsements: ${response.statusText}`);
    }
    const result = await response.json();
    if (result?.status === true && Array.isArray(result.data?.formValues)) {
      return result.data.formValues;
    }
    return [];
  } catch (error) {
    console.error("Error fetching endorsements:", error);
    return [];
  }
};

export const fetchCdlRestrictions = async (): Promise<FormValue[]> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/cdl-restrictions-driver`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch CDL restrictions");
    }
    const result = await response.json();
    return result?.data?.formValues ?? [];
  } catch (error) {
    console.error("Error fetching CDL restrictions:", error);
    return [];
  }
};

export const fetchLanguages = async (): Promise<FormValue[]> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/primary-language-spoken-driver`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch laguages");
    }

    const result = await response.json();
    return result?.data.formValues ?? [];
  } catch (error) {
    console.error("Failed to fetch languages ", error);
    return [];
  }
};


export const fetchOtherLanguages = async (): Promise<FormValue[]> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/other-languages-spoken-fluently-driver`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch languages");
    }

    const result = await response.json();
    return result?.data.formValues ?? [];
  } catch (error) {
    console.error("Failed to fetch other languages", error);
    return [];
  }
};

export const fetchLeavingOptions = async (): Promise<FormValue[]> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/reason-for-leaving-driver-cdl`
    );

    if (!response.ok) {
      throw new Error("Failed to fetch reason options");
    }
    const result = await response.json();
    return result?.data.formValues ?? [];
  } catch (error) {
    console.error("Failed to fetch reason options", error);
    return [];
  }
};

export const fetchDotMedicalCard = async (): Promise<FormValue[]> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/dot-medical-card-status-driver-cdl`
    );

    if (!response.ok) {
      throw new Error("Failed to fecth dot medical options");
    }
    const result = await response.json();
    return result?.data.formValues ?? [];
  } catch (error) {
    console.error("Failed to fecth dot medical options", error);
    return [];
  }
};

export const fetchFormfieldOption=async(slug:string) :Promise<FormValue[]>=>{
  try{
    const response= await fetch(
       `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/${slug} `
    );
    if(!response.ok){
      throw new Error("Failed to fetch formfields options   ");
    }
    const result = await response.json();
    return result?.data.formValues ?? []
  } catch (error){
    console.error("Failed to fetch formfields options  ",error);
    return []
  }
}

export const fetchSchoolBusDriverFormFields = async (): Promise<Record<string, FormValue[]>> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/get-all-by-slug`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          slugs: [
            "age-groups-transported-driver-school-bus-driver",
            "special-needs-transport-experience-driver-school-bus-driver",
            "route-types-driven-driver-school-bus-driver",
            "transmission-type-experience-driver-school-bus-driver",
            "additional-skills-experience-driver-school-bus-driver",
            "type-of-period-driver-school-bus-driver",
            "reason-for-leaving-ending-period-driver-school-bus-driver",
            "dot-medical-card-status-driver-cdl",
            "preferred-employment-types-driver-cdl",
            "preferred-route-types-driver-cdl",
            "when-are-you-available-to-start-driver-cdl",
            "preferred-employment-type-driver-cdl"
          ],
          categorySlug: "school-bus-driver",
          type: "driver"
        })
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch school bus driver form fields");
    }

    const result = await response.json();

    const formFields: Record<string, FormValue[]> = {};

    if (result?.status === true && result?.data) {
      Object.keys(result.data).forEach(slug => {
        if (result.data[slug]?.formValues) {
          formFields[slug] = result.data[slug].formValues;
        }
      });
    }

    return formFields;
  } catch (error) {
    console.error("Failed to fetch school bus driver form fields:", error);
    return {};
  }
};  

// for bus aide/assistant 

export const fetchSchoolBusAideFormFields = async (): Promise<Record<string, FormValue[]>> => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/get-all-by-slug`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          slugs: [
          "supervision-safety-responsibilities-driver-bus-aide-assistant",
        "age-groups-transported-driver-school-bus-driver",
        "special-needs-transport-experience-driver-school-bus-driver",
        "assistance-care-driver-bus-aide-assistant",
        "other-driver-bus-aide-assistant",
        "physical-ability-driver-bus-aide-assistant",
        "type-of-period-driver-school-bus-driver",
        "reason-for-leaving-ending-period-driver-school-bus-driver",
        "dot-medical-card-status-driver-cdl",
        "cpr-certification-type-driver-bus-aide-assistant",
        "first-aid-certification-type-driver-bus-aide-assistant",
        "passenger-assistance-training-pats-ctaa-pass-driver-bus-aide-assistant",
        "state-required-clearances-training-for-school-personnel-driver-bus-aide-assistant",
         "preferred-employment-types-driver-cdl",
        "preferred-route-types-driver-cdl",
        "when-are-you-available-to-start-driver-cdl",
        "preferred-employment-type-driver-cdl",
        "preferred-schedule-driver-bus-aide-assistant"
          ],
          categorySlug: "bus-aide-assistant",
          type: "driver"
        })
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch school bus driver form fields");
    }

    const result = await response.json();

    const formFields: Record<string, FormValue[]> = {};

    if (result?.status === true && result?.data) {
      Object.keys(result.data).forEach(slug => {
        if (result.data[slug]?.formValues) {
          formFields[slug] = result.data[slug].formValues;
        }
      });
    }

    return formFields;
  } catch (error) {
    console.error("Failed to fetch school bus driver form fields:", error);
    return {};
  }
};

export const fetchNemtDriverFormFields = async (slugs?: string[]): Promise<any> => {
  try {
    const defaultSlugs = [
      "types-of-nemt-vehicles-driven-driver-non-emergency-medical-transportation",
      "types-of-passengers-transported-driver-non-emergency-medical-transportation",
      "assistance-skills-driver-non-emergency-medical-transportation",
      "trip-types-destinations-driver-non-emergency-medical-transportation",
      "transmission-type-experience-driver-school-bus-driver",
      "additional-skills-experience-driver-school-bus-driver"
    ];

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_V1}form/form-fields/get-all-by-slug`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          slugs: slugs || defaultSlugs,
          categorySlug: "non-emergency-medical-transportation",
          type: "driver"
        })
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch NEMT driver form fields");
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Failed to fetch NEMT driver form fields:", error);
    return {};
  }
};
