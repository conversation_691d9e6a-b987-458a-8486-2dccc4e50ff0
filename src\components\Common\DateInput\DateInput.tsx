'use client';
import React, { forwardRef } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import { FaCalendarAlt } from 'react-icons/fa';
import css from './DataInput.module.scss';

interface DateInputProps {
  selected: Date | null;
  onChange: (date: Date | null) => void;
  placeholder?: string;
  name?: string;
  minDate?: Date;
  disabled?: boolean;
  dateFormat?: string;
}

interface CustomInputProps {
  value?: string;
  onClick?: () => void;
  disabled?: boolean;
}

// ✅ Forward ref and properly type props
const CustomInput = forwardRef<HTMLDivElement, CustomInputProps>(
  ({ value, onClick, disabled }, ref) => (
    <div
      className={css.dateInputWrapper}
      onClick={!disabled ? onClick : undefined}
      ref={ref}
    >
      <input
        type="text"
        value={value}
        onChange={() => {}}
        placeholder="MM-DD-YYYY"
        className={css.input}
        readOnly
        disabled={disabled}
      />
      <FaCalendarAlt className={css.calendarIcon} />
    </div>
  )
);

// ✅ Set display name to fix the ESLint error
CustomInput.displayName = 'CustomInput';

const DateInput: React.FC<DateInputProps> = ({
  selected,
  onChange,
  placeholder = 'MM-DD-YYYY',
  name = 'date',
  minDate = new Date(),
  disabled = false,
  dateFormat = 'MM-dd-yyyy'
}) => {
  return (
    <DatePicker
      selected={selected}
      onChange={onChange}
      minDate={minDate}
      dateFormat={dateFormat}
      name={name}
      placeholderText={placeholder}
      disabled={disabled}
      customInput={<CustomInput disabled={disabled} />}
      showMonthDropdown
      showYearDropdown
      dropdownMode="select"
    />
  );
};

export default DateInput;
